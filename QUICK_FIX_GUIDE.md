# FocusGuard Pro - Quick Fix Guide

## 🚨 Issues Fixed

### 1. Site Blocking Not Working
**Problem**: Sites weren't being blocked even when added to the blocked list.

**Root Causes**:
- Background script wasn't properly checking blocked sites
- Domain extraction was failing for some URL formats
- Event listeners weren't properly set up
- Debugging was insufficient

**Fixes Applied**:
- ✅ Enhanced `isSiteBlocked()` method with detailed logging
- ✅ Improved `extractDomain()` to handle URLs without protocols
- ✅ Added comprehensive console logging for debugging
- ✅ Fixed event listener setup in background script

### 2. Settings Not Saving
**Problem**: Changes in settings page weren't being saved or applied.

**Root Causes**:
- Nested settings (like `focusMode.autoStartBreaks`) weren't handled properly
- Settings UI wasn't reading saved values correctly
- Toggle switches weren't updating properly

**Fixes Applied**:
- ✅ Enhanced `updateSetting()` method to handle nested properties
- ✅ Fixed `updateSettingsUI()` to read nested settings
- ✅ Added proper toggle state management
- ✅ Added debugging for settings operations

### 3. Dummy Data in UI
**Problem**: Statistics and site information showed random/dummy data instead of real data.

**Root Causes**:
- `getBlockStatus()` was generating fake data
- Statistics were using random numbers
- Real usage data wasn't being loaded properly

**Fixes Applied**:
- ✅ Updated `getBlockStatus()` to show real temporary unblock status
- ✅ Fixed statistics to calculate from actual usage data
- ✅ Enhanced data loading to include all relevant information

## 🔧 Testing Instructions

### Step 1: Load the Extension
1. Open Chrome and go to `chrome://extensions/`
2. Enable "Developer mode"
3. Click "Load unpacked" and select the FocusGuard Pro folder
4. Verify the extension appears and is enabled

### Step 2: Test Basic Blocking
1. Click the FocusGuard Pro icon
2. Add a test site (e.g., "example.com")
3. Open a new tab and go to `https://example.com`
4. You should be redirected to the blocked page

### Step 3: Debug if Not Working
1. Open the test page: `test-blocking.html`
2. Follow the instructions on the page
3. Open browser console (F12) and run the debug script:
   ```javascript
   // Copy and paste the contents of debug-extension.js
   ```

### Step 4: Check Background Script
1. Go to `chrome://extensions/`
2. Find FocusGuard Pro and click "service worker"
3. Check for any error messages in the console
4. Look for the initialization logs

## 🐛 Common Issues & Solutions

### Issue: "Extension not responding"
**Solution**: 
- Reload the extension in `chrome://extensions/`
- Check for JavaScript errors in background script console

### Issue: "Sites not blocking immediately"
**Solution**:
- Refresh the page after adding a site to blocked list
- Check that the domain was extracted correctly in console logs

### Issue: "Settings not saving"
**Solution**:
- Check browser console for error messages
- Verify chrome.storage permissions in manifest.json
- Try clearing extension data and reconfiguring

### Issue: "Focus mode not working"
**Solution**:
- Check that alarms permission is granted
- Verify focus mode window opens correctly
- Check background script console for timer errors

## 📊 Debug Information

### Console Commands for Testing
```javascript
// Test extension communication
chrome.runtime.sendMessage({action: 'getBlockedSites'}, console.log);

// Test adding a site
chrome.runtime.sendMessage({action: 'addBlockedSite', url: 'test.com'}, console.log);

// Test site blocking check
chrome.runtime.sendMessage({action: 'checkSiteStatus', url: 'https://test.com'}, console.log);

// Check storage
chrome.storage.local.get(null, console.log);
```

### Expected Console Output
When working correctly, you should see:
```
FocusGuard Pro initialized with X blocked sites
Blocked sites: ["example.com", "facebook.com"]
Extension enabled: true
```

### Background Script Logs
Look for these messages in the service worker console:
- "FocusGuard Pro initialized"
- "Checking site: [URL]"
- "Site is blocked, redirecting: [URL]"
- "Successfully redirected to blocked page"

## 🔄 Reset Instructions

If the extension is completely broken:

1. **Clear Extension Data**:
   ```javascript
   chrome.storage.local.clear();
   ```

2. **Reload Extension**:
   - Go to `chrome://extensions/`
   - Click reload button for FocusGuard Pro

3. **Reconfigure**:
   - Add blocked sites again
   - Configure settings as needed

## ✅ Verification Checklist

- [ ] Extension loads without errors
- [ ] Can add sites to blocked list
- [ ] Sites are actually blocked when visited
- [ ] Settings save and persist
- [ ] Focus mode opens and works
- [ ] Statistics show real data
- [ ] Temporary unblocks work with OTP
- [ ] Password protection functions

## 📞 Additional Support

If issues persist:

1. **Check Browser Console**: Look for JavaScript errors
2. **Verify Permissions**: Ensure all required permissions are granted
3. **Test in Incognito**: Try the extension in incognito mode
4. **Clear Browser Cache**: Sometimes helps with extension issues
5. **Restart Browser**: Full browser restart can resolve some issues

---

**Note**: The fixes applied should resolve the major blocking and settings issues. The extension now includes comprehensive debugging to help identify any remaining problems.
