/* FocusGuard Pro - Blocked Page Styles */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #2d3748;
}

.blocked-container {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Header */
.blocked-header {
    padding: 20px;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
}

.logo {
    display: flex;
    align-items: center;
    gap: 12px;
    color: white;
}

.shield-icon {
    width: 32px;
    height: 32px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
}

.logo h1 {
    font-size: 20px;
    font-weight: 600;
}

/* Main Content */
.blocked-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px 20px;
    text-align: center;
}

.blocked-icon {
    margin-bottom: 24px;
}

.icon-circle {
    width: 120px;
    height: 120px;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.block-symbol {
    font-size: 48px;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

.blocked-title {
    font-size: 32px;
    font-weight: 700;
    color: white;
    margin-bottom: 12px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.blocked-message {
    font-size: 18px;
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 32px;
    max-width: 500px;
    line-height: 1.6;
}

.blocked-site-info {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 12px;
    padding: 24px;
    margin-bottom: 32px;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 24px;
    max-width: 500px;
    width: 100%;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.site-details h3,
.block-time h3 {
    font-size: 14px;
    font-weight: 600;
    color: #4a5568;
    margin-bottom: 8px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.blocked-url {
    font-size: 16px;
    font-weight: 500;
    color: #c53030;
    word-break: break-all;
}

.block-timestamp {
    font-size: 16px;
    font-weight: 500;
    color: #2d3748;
}

/* Action Buttons */
.action-buttons {
    display: flex;
    gap: 16px;
    margin-bottom: 48px;
    flex-wrap: wrap;
    justify-content: center;
}

.btn {
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    text-decoration: none;
    min-width: 140px;
    justify-content: center;
}

.btn-primary {
    background: rgba(255, 255, 255, 0.9);
    color: #667eea;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.btn-primary:hover {
    background: white;
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.25);
}

.btn-secondary {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: 2px solid rgba(255, 255, 255, 0.3);
}

.btn-secondary:hover {
    background: rgba(255, 255, 255, 0.3);
    border-color: rgba(255, 255, 255, 0.5);
    transform: translateY(-2px);
}

.btn-ghost {
    background: transparent;
    color: rgba(255, 255, 255, 0.8);
    border: 1px solid rgba(255, 255, 255, 0.3);
    padding: 8px 16px;
    font-size: 12px;
    min-width: auto;
}

.btn-ghost:hover {
    background: rgba(255, 255, 255, 0.1);
    color: white;
}

.btn-icon {
    font-size: 16px;
}

/* Productivity Tips */
.productivity-tips {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 12px;
    padding: 24px;
    margin-bottom: 32px;
    max-width: 600px;
    width: 100%;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.productivity-tips h3 {
    font-size: 18px;
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 16px;
    text-align: center;
}

.tips-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(160px, 1fr));
    gap: 16px;
}

.tip-card {
    text-align: center;
    padding: 16px;
    background: #f7fafc;
    border-radius: 8px;
    border: 1px solid #edf2f7;
}

.tip-icon {
    font-size: 24px;
    margin-bottom: 8px;
}

.tip-card h4 {
    font-size: 14px;
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 4px;
}

.tip-card p {
    font-size: 12px;
    color: #718096;
    line-height: 1.4;
}

/* Quick Links */
.quick-links {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 12px;
    padding: 24px;
    max-width: 600px;
    width: 100%;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.quick-links h3 {
    font-size: 18px;
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 16px;
    text-align: center;
}

.links-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 12px;
}

.quick-link {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 16px 12px;
    background: #f7fafc;
    border-radius: 8px;
    text-decoration: none;
    color: #2d3748;
    transition: all 0.2s ease;
    border: 1px solid #edf2f7;
}

.quick-link:hover {
    background: #edf2f7;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.link-icon {
    font-size: 20px;
    margin-bottom: 6px;
}

/* Footer */
.blocked-footer {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    padding: 20px;
    text-align: center;
}

.footer-actions {
    display: flex;
    justify-content: center;
    gap: 16px;
    margin-bottom: 12px;
}

.footer-text {
    color: rgba(255, 255, 255, 0.8);
    font-size: 12px;
}

.footer-text a {
    color: rgba(255, 255, 255, 0.9);
    text-decoration: underline;
}

.footer-text a:hover {
    color: white;
}

/* Modal */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.modal-overlay.show {
    display: flex;
}

.modal {
    background: white;
    border-radius: 12px;
    max-width: 400px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.modal-header {
    padding: 20px 24px 16px;
    border-bottom: 1px solid #edf2f7;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    font-size: 18px;
    font-weight: 600;
    color: #2d3748;
}

.modal-close {
    background: none;
    border: none;
    font-size: 24px;
    color: #718096;
    cursor: pointer;
    padding: 0;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-close:hover {
    color: #2d3748;
}

.modal-content {
    padding: 24px;
}

.modal-content p {
    margin-bottom: 20px;
    color: #4a5568;
    line-height: 1.6;
}

.unblock-options {
    display: flex;
    flex-direction: column;
    gap: 8px;
    margin-bottom: 20px;
}

.option-btn {
    padding: 12px 16px;
    background: #f7fafc;
    border: 1px solid #edf2f7;
    border-radius: 6px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.2s ease;
    text-align: left;
}

.option-btn:hover {
    background: #edf2f7;
    border-color: #667eea;
}

.modal-note {
    background: #fef5e7;
    border: 1px solid #f6e05e;
    border-radius: 6px;
    padding: 12px;
}

.modal-note p {
    font-size: 12px;
    color: #744210;
    margin: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
    .blocked-site-info {
        grid-template-columns: 1fr;
        gap: 16px;
    }
    
    .action-buttons {
        flex-direction: column;
        align-items: center;
    }
    
    .tips-grid,
    .links-grid {
        grid-template-columns: 1fr;
    }
    
    .footer-actions {
        flex-direction: column;
        gap: 8px;
    }
}
