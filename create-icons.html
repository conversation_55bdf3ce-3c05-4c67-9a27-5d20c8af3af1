<!DOCTYPE html>
<html>
<head>
    <title>FocusGuard Pro - Icon Generator</title>
</head>
<body>
    <h1>FocusGuard Pro Icon Generator</h1>
    <p>This page generates basic icons for the extension. Open this in a browser and use the download buttons.</p>
    
    <div id="icons"></div>
    
    <script>
        function createIcon(size) {
            const canvas = document.createElement('canvas');
            canvas.width = size;
            canvas.height = size;
            const ctx = canvas.getContext('2d');
            
            // Create gradient background
            const gradient = ctx.createLinearGradient(0, 0, size, size);
            gradient.addColorStop(0, '#667eea');
            gradient.addColorStop(1, '#764ba2');
            
            // Fill background
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, size, size);
            
            // Add shield icon
            ctx.fillStyle = 'white';
            ctx.font = `${size * 0.6}px Arial`;
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText('🛡️', size / 2, size / 2);
            
            return canvas;
        }
        
        function downloadIcon(canvas, filename) {
            const link = document.createElement('a');
            link.download = filename;
            link.href = canvas.toDataURL();
            link.click();
        }
        
        // Create icons
        const sizes = [16, 48, 128];
        const iconsDiv = document.getElementById('icons');
        
        sizes.forEach(size => {
            const canvas = createIcon(size);
            const container = document.createElement('div');
            container.style.margin = '20px';
            
            const title = document.createElement('h3');
            title.textContent = `${size}x${size} Icon`;
            
            const button = document.createElement('button');
            button.textContent = `Download icon${size}.png`;
            button.onclick = () => downloadIcon(canvas, `icon${size}.png`);
            
            container.appendChild(title);
            container.appendChild(canvas);
            container.appendChild(document.createElement('br'));
            container.appendChild(button);
            
            iconsDiv.appendChild(container);
        });
    </script>
</body>
</html>
