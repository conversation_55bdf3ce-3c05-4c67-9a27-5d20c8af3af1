// FocusGuard Pro - Options Page JavaScript

class FocusGuardOptions {
  constructor() {
    this.blockedSites = [];
    this.settings = {};
    this.isEnabled = true;
    this.init();
  }

  async init() {
    // Show loading
    this.showLoading(true);

    try {
      // Load data
      await this.loadData();

      // Setup navigation
      this.setupNavigation();

      // Setup event listeners
      this.setupEventListeners();

      // Update UI
      this.updateUI();

      // Hide loading
      this.showLoading(false);
    } catch (error) {
      console.error('Options initialization error:', error);
      this.showLoading(false);
    }
  }

  async loadData() {
    try {
      // Load blocked sites and additional data
      const response = await chrome.runtime.sendMessage({ action: 'getBlockedSites' });
      console.log('Options page received data:', response);
      if (response) {
        this.blockedSites = response.sites || [];
        this.isEnabled = response.isEnabled !== false;
        this.temporaryUnblocks = response.temporaryUnblocks || {};
        this.usageTracking = response.usageTracking || {};
      }

      // Load usage stats
      const usageResponse = await chrome.runtime.sendMessage({ action: 'getUsageStats' });
      if (usageResponse) {
        this.usageTracking = usageResponse.usage || {};
        this.dailyLimits = usageResponse.limits || {};
      }

      // Load settings using storage manager
      this.settings = await storageManager.getSettings();
      console.log('Options page loaded settings:', this.settings);
      console.log('Options page loaded:', this.blockedSites.length, 'blocked sites');
    } catch (error) {
      console.error('Error loading data:', error);
    }
  }

  setupNavigation() {
    const navLinks = document.querySelectorAll('.nav-link');

    navLinks.forEach(link => {
      link.addEventListener('click', (e) => {
        e.preventDefault();
        const sectionId = link.getAttribute('data-section');
        this.showSection(sectionId);

        // Update active nav link
        navLinks.forEach(l => l.classList.remove('active'));
        link.classList.add('active');
      });
    });
  }

  showSection(sectionId) {
    // Hide all sections
    const sections = document.querySelectorAll('.content-section');
    sections.forEach(section => section.classList.remove('active'));

    // Show target section
    const targetSection = document.getElementById(sectionId);
    if (targetSection) {
      targetSection.classList.add('active');
    }
  }

  setupEventListeners() {
    // Main protection toggle
    const mainToggle = document.getElementById('mainProtectionToggle');
    if (mainToggle) {
      mainToggle.addEventListener('click', () => this.toggleMainProtection());
    }

    // Add site button
    const addSiteBtn = document.getElementById('addSiteBtn');
    if (addSiteBtn) {
      addSiteBtn.addEventListener('click', () => this.addSite());
    }

    // New site input enter key
    const newSiteInput = document.getElementById('newSiteInput');
    if (newSiteInput) {
      newSiteInput.addEventListener('keypress', (e) => {
        if (e.key === 'Enter') {
          this.addSite();
        }
      });
    }

    // Settings toggles
    this.setupToggle('incognitoToggle', 'workInIncognito');
    this.setupToggle('notificationsToggle', 'showNotifications');
    this.setupToggle('analyticsToggle', 'enableTimeTracking');
    this.setupToggle('timeLimitsToggle', 'enableTimeLimits');
    this.setupToggle('autoStartBreaksToggle', 'focusMode.autoStartBreaks');
    this.setupToggle('passwordProtectionToggle', 'passwordProtection');

    // Settings dropdowns
    const defaultBlockDuration = document.getElementById('defaultBlockDuration');
    if (defaultBlockDuration) {
      defaultBlockDuration.addEventListener('change', () => {
        this.updateSetting('defaultBlockDuration', defaultBlockDuration.value);
      });
    }

    const defaultDailyLimit = document.getElementById('defaultDailyLimit');
    if (defaultDailyLimit) {
      defaultDailyLimit.addEventListener('change', () => {
        this.updateSetting('defaultDailyLimit', parseInt(defaultDailyLimit.value.replace(' minutes', '').replace(' hours', '') * (defaultDailyLimit.value.includes('hours') ? 60 : 1)));
      });
    }

    const workSessionDuration = document.getElementById('workSessionDuration');
    if (workSessionDuration) {
      workSessionDuration.addEventListener('change', () => {
        this.updateSetting('focusMode.workDuration', parseInt(workSessionDuration.value.replace(' minutes', '')));
      });
    }

    const breakDuration = document.getElementById('breakDuration');
    if (breakDuration) {
      breakDuration.addEventListener('change', () => {
        this.updateSetting('focusMode.breakDuration', parseInt(breakDuration.value.replace(' minutes', '')));
      });
    }

    // Action buttons
    const exportDataBtn = document.getElementById('exportDataBtn');
    if (exportDataBtn) {
      exportDataBtn.addEventListener('click', () => this.exportData());
    }

    const importDataBtn = document.getElementById('importDataBtn');
    if (importDataBtn) {
      importDataBtn.addEventListener('click', () => this.importData());
    }

    const resetSettingsBtn = document.getElementById('resetSettingsBtn');
    if (resetSettingsBtn) {
      resetSettingsBtn.addEventListener('click', () => this.resetSettings());
    }

    const clearDataBtn = document.getElementById('clearDataBtn');
    if (clearDataBtn) {
      clearDataBtn.addEventListener('click', () => this.clearAllData());
    }

    const updatePasswordBtn = document.getElementById('updatePasswordBtn');
    if (updatePasswordBtn) {
      updatePasswordBtn.addEventListener('click', () => this.updateMasterPassword());
    }

    // File input for import
    const importFileInput = document.getElementById('importFileInput');
    if (importFileInput) {
      importFileInput.addEventListener('change', (e) => this.handleFileImport(e));
    }
  }

  setupToggle(toggleId, settingKey) {
    const toggle = document.getElementById(toggleId);
    if (toggle) {
      toggle.addEventListener('click', () => {
        const isActive = toggle.classList.contains('active');
        this.updateSetting(settingKey, !isActive);

        if (isActive) {
          toggle.classList.remove('active');
        } else {
          toggle.classList.add('active');
        }
      });
    }
  }

  updateUI() {
    this.updateStats();
    this.updateMainToggle();
    this.updateBlockedSitesList();
    this.updateSettingsUI();
  }

  updateStats() {
    // Update stats in overview section
    const blockedSitesCount = document.getElementById('blockedSitesCount');
    if (blockedSitesCount) {
      blockedSitesCount.textContent = this.blockedSites.length;
    }

    // Calculate real stats from usage data
    const blocksToday = document.getElementById('blocksToday');
    if (blocksToday) {
      // Count how many blocked sites have usage data (indicating block attempts)
      let blockCount = 0;
      if (this.usageTracking) {
        for (const domain of this.blockedSites) {
          if (this.usageTracking[domain]) {
            blockCount++;
          }
        }
      }
      blocksToday.textContent = blockCount;
    }

    const timeSaved = document.getElementById('timeSaved');
    if (timeSaved) {
      // Calculate total time that would have been spent on blocked sites
      let totalMinutes = 0;
      if (this.usageTracking) {
        for (const domain of this.blockedSites) {
          if (this.usageTracking[domain]) {
            totalMinutes += this.usageTracking[domain];
          }
        }
      }
      const hours = Math.floor(totalMinutes / 60);
      timeSaved.textContent = hours > 0 ? `${hours}h` : `${totalMinutes}m`;
    }

    const successRate = document.getElementById('successRate');
    if (successRate) {
      // Calculate success rate based on blocked vs total sites
      const totalSites = this.blockedSites.length;
      const activelySites = Object.keys(this.usageTracking || {}).length;
      const rate = totalSites > 0 ? Math.round((totalSites / Math.max(totalSites, activelySites)) * 100) : 100;
      successRate.textContent = `${Math.min(rate, 100)}%`;
    }
  }

  updateMainToggle() {
    const mainToggle = document.getElementById('mainProtectionToggle');
    if (mainToggle) {
      if (this.isEnabled) {
        mainToggle.classList.add('active');
      } else {
        mainToggle.classList.remove('active');
      }
    }
  }

  updateBlockedSitesList() {
    const sitesList = document.getElementById('blockedSitesList');
    const emptyState = document.getElementById('emptyBlockedSites');

    if (!sitesList) return;

    if (this.blockedSites.length === 0) {
      sitesList.innerHTML = '';
      sitesList.appendChild(emptyState);
      return;
    }

    sitesList.innerHTML = '';

    this.blockedSites.forEach(domain => {
      const siteItem = this.createSiteItem(domain);
      sitesList.appendChild(siteItem);
    });
  }

  createSiteItem(domain) {
    const item = document.createElement('div');
    item.className = 'site-item';

    const category = this.getSiteCategory(domain);
    const blockStatus = this.getBlockStatus(domain);

    item.innerHTML = `
      <div class="site-info">
        <div class="site-icon" style="background: ${this.getSiteColor(domain)};">
          ${this.getSiteIcon(domain)}
        </div>
        <div class="site-details">
          <h5>${domain}</h5>
          <p>${category}</p>
        </div>
      </div>
      <div class="time-badge">${blockStatus}</div>
    `;

    // Add remove functionality on click
    item.addEventListener('click', (e) => {
      if (e.target.closest('.time-badge')) {
        this.removeSite(domain);
      }
    });

    return item;
  }

  updateSettingsUI() {
    console.log('Updating settings UI with:', this.settings);

    // Update toggle states
    const toggles = {
      'incognitoToggle': this.settings.workInIncognito,
      'notificationsToggle': this.settings.showNotifications,
      'analyticsToggle': this.settings.enableTimeTracking,
      'timeLimitsToggle': this.settings.enableTimeLimits,
      'autoStartBreaksToggle': this.settings.focusMode?.autoStartBreaks,
      'passwordProtectionToggle': this.settings.passwordProtection
    };

    Object.entries(toggles).forEach(([toggleId, value]) => {
      const toggle = document.getElementById(toggleId);
      if (toggle) {
        console.log('Setting toggle', toggleId, 'to', value);
        if (value) {
          toggle.classList.add('active');
        } else {
          toggle.classList.remove('active');
        }
      }
    });

    // Update dropdown values
    const defaultBlockDuration = document.getElementById('defaultBlockDuration');
    if (defaultBlockDuration) {
      defaultBlockDuration.value = this.settings.defaultBlockDuration || '5days';
    }

    const defaultDailyLimit = document.getElementById('defaultDailyLimit');
    if (defaultDailyLimit) {
      const limitMinutes = this.settings.defaultDailyLimit || 120;
      if (limitMinutes >= 60) {
        defaultDailyLimit.value = `${Math.floor(limitMinutes / 60)} hours`;
      } else {
        defaultDailyLimit.value = `${limitMinutes} minutes`;
      }
    }

    const workSessionDuration = document.getElementById('workSessionDuration');
    if (workSessionDuration) {
      workSessionDuration.value = `${this.settings.focusMode?.workDuration || 25} minutes`;
    }

    const breakDuration = document.getElementById('breakDuration');
    if (breakDuration) {
      breakDuration.value = `${this.settings.focusMode?.breakDuration || 5} minutes`;
    }
  }

  async toggleMainProtection() {
    this.showLoading(true);

    try {
      const response = await chrome.runtime.sendMessage({ action: 'toggleBlocking' });
      if (response && response.success) {
        this.isEnabled = response.isEnabled;
        this.updateMainToggle();
        this.showNotification(
          this.isEnabled ? 'Protection enabled' : 'Protection disabled',
          'success'
        );
      }
    } catch (error) {
      console.error('Error toggling protection:', error);
      this.showNotification('Failed to toggle protection', 'error');
    }

    this.showLoading(false);
  }

  async addSite() {
    const input = document.getElementById('newSiteInput');
    const url = input.value.trim();

    if (!url) return;

    const domain = this.extractDomain(url) || url;

    if (this.blockedSites.includes(domain)) {
      this.showNotification('Site is already blocked', 'warning');
      return;
    }

    this.showLoading(true);

    try {
      const response = await chrome.runtime.sendMessage({
        action: 'addBlockedSite',
        url: domain
      });

      if (response && response.success) {
        this.blockedSites.push(domain);
        input.value = '';
        this.updateUI();
        this.showNotification(`${domain} has been blocked`, 'success');
      }
    } catch (error) {
      console.error('Error adding site:', error);
      this.showNotification('Failed to block site', 'error');
    }

    this.showLoading(false);
  }

  async removeSite(domain) {
    this.showLoading(true);

    try {
      const response = await chrome.runtime.sendMessage({
        action: 'removeBlockedSite',
        url: domain
      });

      if (response && response.success) {
        this.blockedSites = this.blockedSites.filter(site => site !== domain);
        this.updateUI();
        this.showNotification(`${domain} has been unblocked`, 'success');
      }
    } catch (error) {
      console.error('Error removing site:', error);
      this.showNotification('Failed to unblock site', 'error');
    }

    this.showLoading(false);
  }

  async updateSetting(key, value) {
    console.log('Updating setting:', key, '=', value);

    // Handle nested settings like 'focusMode.autoStartBreaks'
    if (key.includes('.')) {
      const [parentKey, childKey] = key.split('.');
      if (!this.settings[parentKey]) {
        this.settings[parentKey] = {};
      }
      this.settings[parentKey][childKey] = value;
    } else {
      this.settings[key] = value;
    }

    try {
      await storageManager.setSettings(this.settings);
      console.log('Setting saved successfully:', this.settings);
      this.showNotification('Setting updated', 'success');
    } catch (error) {
      console.error('Error updating setting:', error);
      this.showNotification('Failed to update setting', 'error');
    }
  }

  async exportData() {
    try {
      const data = await storageManager.exportData();
      if (data) {
        const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `focusguard-backup-${new Date().toISOString().split('T')[0]}.json`;
        a.click();
        URL.revokeObjectURL(url);
        this.showNotification('Data exported successfully', 'success');
      }
    } catch (error) {
      console.error('Error exporting data:', error);
      this.showNotification('Failed to export data', 'error');
    }
  }

  importData() {
    const fileInput = document.getElementById('importFileInput');
    if (fileInput) {
      fileInput.click();
    }
  }

  async handleFileImport(event) {
    const file = event.target.files[0];
    if (!file) return;

    try {
      const text = await file.text();
      const data = JSON.parse(text);

      if (await storageManager.importData(data)) {
        this.showNotification('Data imported successfully', 'success');
        // Reload the page to reflect changes
        setTimeout(() => window.location.reload(), 1500);
      } else {
        this.showNotification('Failed to import data', 'error');
      }
    } catch (error) {
      console.error('Error importing data:', error);
      this.showNotification('Invalid file format', 'error');
    }
  }

  async resetSettings() {
    if (!confirm('Are you sure you want to reset all settings to default values?')) {
      return;
    }

    try {
      this.settings = storageManager.getDefaultSettings();
      await storageManager.setSettings(this.settings);
      this.updateSettingsUI();
      this.showNotification('Settings reset to defaults', 'success');
    } catch (error) {
      console.error('Error resetting settings:', error);
      this.showNotification('Failed to reset settings', 'error');
    }
  }

  async clearAllData() {
    if (!confirm('Are you sure you want to permanently delete all data? This action cannot be undone.')) {
      return;
    }

    try {
      await storageManager.clear();
      this.showNotification('All data cleared', 'success');
      // Reload the page
      setTimeout(() => window.location.reload(), 1500);
    } catch (error) {
      console.error('Error clearing data:', error);
      this.showNotification('Failed to clear data', 'error');
    }
  }

  async updateMasterPassword() {
    const passwordInput = document.getElementById('masterPassword');
    const password = passwordInput.value.trim();

    if (!password) {
      this.showNotification('Please enter a password', 'warning');
      return;
    }

    if (password.length < 6) {
      this.showNotification('Password must be at least 6 characters', 'warning');
      return;
    }

    this.showLoading(true);

    try {
      const response = await chrome.runtime.sendMessage({
        action: 'setMasterPassword',
        password: password
      });

      if (response && response.success) {
        passwordInput.value = '';
        this.showNotification('Master password updated successfully', 'success');
      } else {
        this.showNotification('Failed to update password', 'error');
      }
    } catch (error) {
      console.error('Error updating password:', error);
      this.showNotification('Failed to update password', 'error');
    }

    this.showLoading(false);
  }

  extractDomain(url) {
    try {
      const urlObj = new URL(url);
      return urlObj.hostname.replace(/^www\./, '');
    } catch (error) {
      return null;
    }
  }

  getSiteIcon(domain) {
    const icons = {
      'facebook.com': 'f',
      'twitter.com': '𝕏',
      'youtube.com': '▶',
      'instagram.com': '📷',
      'reddit.com': '🤖',
      'tiktok.com': '🎵'
    };
    return icons[domain] || '🌐';
  }

  getSiteColor(domain) {
    const colors = {
      'facebook.com': '#1877F2',
      'twitter.com': '#1DA1F2',
      'youtube.com': '#FF0000',
      'instagram.com': '#E4405F',
      'reddit.com': '#FF4500',
      'tiktok.com': '#000000'
    };
    return colors[domain] || '#667eea';
  }

  getSiteCategory(domain) {
    const categories = {
      'facebook.com': 'Social Media',
      'twitter.com': 'Social Media',
      'instagram.com': 'Social Media',
      'tiktok.com': 'Social Media',
      'youtube.com': 'Entertainment',
      'reddit.com': 'Social Media',
      'netflix.com': 'Entertainment',
      'twitch.tv': 'Entertainment',
      'amazon.com': 'Shopping',
      'ebay.com': 'Shopping'
    };
    return categories[domain] || 'Website';
  }

  getBlockStatus(domain) {
    // Check if temporarily unblocked
    if (this.temporaryUnblocks && this.temporaryUnblocks[domain]) {
      const endTime = this.temporaryUnblocks[domain];
      const now = Date.now();
      if (now < endTime) {
        const remainingMinutes = Math.ceil((endTime - now) / (1000 * 60));
        if (remainingMinutes > 60) {
          const hours = Math.ceil(remainingMinutes / 60);
          return `${hours}h left`;
        } else {
          return `${remainingMinutes}m left`;
        }
      }
    }

    // Check if has daily limit
    if (this.settings.enableTimeLimits && this.dailyLimits && this.dailyLimits[domain]) {
      return `${this.dailyLimits[domain]}min daily limit`;
    }

    // Default status
    return 'Permanently blocked';
  }

  showLoading(show) {
    const overlay = document.getElementById('loadingOverlay');
    if (overlay) {
      if (show) {
        overlay.classList.add('show');
      } else {
        overlay.classList.remove('show');
      }
    }
  }

  showNotification(message, type = 'info') {
    // Create a simple notification system
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.textContent = message;

    // Add styles
    notification.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      padding: 12px 20px;
      border-radius: 8px;
      color: white;
      font-weight: 500;
      z-index: 10000;
      animation: slideIn 0.3s ease;
      max-width: 300px;
      word-wrap: break-word;
    `;

    // Set background color based on type
    switch (type) {
      case 'success':
        notification.style.background = '#48bb78';
        break;
      case 'error':
        notification.style.background = '#f56565';
        break;
      case 'warning':
        notification.style.background = '#ed8936';
        break;
      case 'info':
      default:
        notification.style.background = '#667eea';
        break;
    }

    // Add animation styles if not already added
    if (!document.getElementById('notification-styles')) {
      const style = document.createElement('style');
      style.id = 'notification-styles';
      style.textContent = `
        @keyframes slideIn {
          from { transform: translateX(100%); opacity: 0; }
          to { transform: translateX(0); opacity: 1; }
        }
        @keyframes slideOut {
          from { transform: translateX(0); opacity: 1; }
          to { transform: translateX(100%); opacity: 0; }
        }
      `;
      document.head.appendChild(style);
    }

    document.body.appendChild(notification);

    // Auto remove after 3 seconds
    setTimeout(() => {
      notification.style.animation = 'slideOut 0.3s ease';
      setTimeout(() => {
        if (notification.parentNode) {
          notification.parentNode.removeChild(notification);
        }
      }, 300);
    }, 3000);
  }
}

// Initialize options page when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  new FocusGuardOptions();
});
