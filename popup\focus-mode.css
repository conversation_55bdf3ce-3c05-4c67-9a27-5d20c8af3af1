/* Focus Mode Specific Styles */

.popup-container {
    width: 420px;
    min-height: 600px;
}

.header-controls {
    display: flex;
    align-items: center;
}

.close-btn {
    background: none;
    border: none;
    color: rgba(255, 255, 255, 0.8);
    font-size: 24px;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.close-btn:hover {
    background: rgba(255, 255, 255, 0.1);
    color: white;
}

#connectionStatus {
    color: rgba(255, 255, 255, 0.9);
    font-weight: 500;
    padding: 2px 6px;
    border-radius: 4px;
    background: rgba(255, 255, 255, 0.1);
}

.focus-content {
    padding: 24px;
}

/* Timer Section */
.timer-section {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 32px;
}

.timer-circle {
    position: relative;
    width: 200px;
    height: 200px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
    border-radius: 50%;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.timer-display {
    text-align: center;
    z-index: 2;
}

.time-text {
    font-size: 32px;
    font-weight: 700;
    color: #2d3748;
    margin-bottom: 4px;
    font-family: 'Courier New', monospace;
}

.session-type {
    font-size: 12px;
    color: #718096;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.progress-ring {
    position: absolute;
    top: 0;
    left: 0;
    width: 200px;
    height: 200px;
    transform: rotate(-90deg);
}

.progress-ring svg {
    width: 100%;
    height: 100%;
}

#progressCircle {
    transition: stroke-dashoffset 1s linear;
}

/* Controls */
.controls {
    display: flex;
    justify-content: center;
    gap: 12px;
    margin-bottom: 32px;
}

.control-btn {
    padding: 12px 20px;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    min-width: 120px;
    justify-content: center;
}

.start-btn {
    background: #48bb78;
    color: white;
}

.start-btn:hover {
    background: #38a169;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(72, 187, 120, 0.3);
}

.pause-btn {
    background: #ed8936;
    color: white;
}

.pause-btn:hover {
    background: #dd6b20;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(237, 137, 54, 0.3);
}

.stop-btn {
    background: #f56565;
    color: white;
}

.stop-btn:hover {
    background: #e53e3e;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(245, 101, 101, 0.3);
}

.hidden {
    display: none !important;
}

/* Session Info */
.session-info {
    background: #f7fafc;
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 24px;
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 16px;
    border: 1px solid #edf2f7;
}

.info-item {
    text-align: center;
}

.info-label {
    display: block;
    font-size: 11px;
    color: #718096;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 4px;
}

.info-value {
    display: block;
    font-size: 18px;
    font-weight: 700;
    color: #2d3748;
}

/* Quick Settings */
.quick-settings {
    background: white;
    border: 1px solid #edf2f7;
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 24px;
}

.quick-settings h3 {
    font-size: 14px;
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 16px;
}

.setting-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
}

.setting-row:last-child {
    margin-bottom: 0;
}

.setting-row label {
    font-size: 13px;
    color: #4a5568;
    font-weight: 500;
}

.setting-select {
    padding: 6px 10px;
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    font-size: 12px;
    background: #fafbfc;
    color: #2d3748;
    min-width: 80px;
}

.setting-select:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* Focus Tips */
.focus-tips {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 12px;
    padding: 20px;
}

.focus-tips h3 {
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 12px;
}

.tip {
    font-size: 13px;
    line-height: 1.5;
    opacity: 0.9;
}

/* Break Mode Styles */
.break-mode .timer-circle {
    background: linear-gradient(135deg, #fed7d7 0%, #feb2b2 100%);
}

.break-mode .time-text {
    color: #c53030;
}

.break-mode #progressCircle {
    stroke: #f56565;
}

.break-mode .session-type {
    color: #c53030;
}

/* Active Session Styles */
.active-session .timer-circle {
    background: linear-gradient(135deg, #c6f6d5 0%, #9ae6b4 100%);
    animation: pulse 2s infinite;
}

.active-session .time-text {
    color: #22543d;
}

.active-session #progressCircle {
    stroke: #48bb78;
}

@keyframes pulse {

    0%,
    100% {
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    }

    50% {
        box-shadow: 0 8px 32px rgba(72, 187, 120, 0.3);
    }
}

/* Paused State */
.paused .timer-circle {
    background: linear-gradient(135deg, #fef5e7 0%, #fbd38d 100%);
}

.paused .time-text {
    color: #744210;
}

.paused #progressCircle {
    stroke: #ed8936;
}

/* Responsive Design */
@media (max-width: 480px) {
    .popup-container {
        width: 100%;
        max-width: 380px;
    }

    .timer-circle {
        width: 160px;
        height: 160px;
    }

    .progress-ring {
        width: 160px;
        height: 160px;
    }

    .progress-ring svg {
        width: 160px;
        height: 160px;
    }

    .time-text {
        font-size: 24px;
    }

    .session-info {
        grid-template-columns: 1fr;
        gap: 12px;
    }

    .controls {
        flex-direction: column;
        align-items: center;
    }

    .control-btn {
        width: 100%;
        max-width: 200px;
    }
}

/* Enhanced Visual States */
.popup-container.offline {
    opacity: 0.9;
}

.popup-container.offline .timer-circle {
    background: linear-gradient(135deg, #fed7d7 0%, #fbb6ce 100%) !important;
}

.popup-container.offline .time-text {
    color: #744210 !important;
}

/* Connection Status Indicator */
#connectionStatus {
    position: relative !important;
    top: auto !important;
    right: auto !important;
}

/* Notification Animations */
@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }

    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideOutRight {
    from {
        transform: translateX(0);
        opacity: 1;
    }

    to {
        transform: translateX(100%);
        opacity: 0;
    }
}

.notification {
    animation: slideInRight 0.3s ease !important;
}

/* Enhanced Button States */
.control-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
}

.control-btn:disabled:hover {
    transform: none !important;
    box-shadow: none !important;
}

/* Better Progress Ring */
.progress-ring svg circle:first-child {
    opacity: 0.3;
}

.progress-ring svg circle:last-child {
    transition: stroke-dashoffset 0.5s ease, stroke 0.3s ease;
}

/* Loading Overlay Enhancements */
.loading-overlay {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(2px);
}

.loading-spinner {
    width: 32px;
    height: 32px;
    border: 3px solid #e2e8f0;
    border-top: 3px solid #667eea;
    margin-bottom: 12px;
}

.loading-overlay p {
    font-size: 14px;
    color: #4a5568;
    font-weight: 500;
}