// FocusGuard Pro - Focus Mode JavaScript

class FocusModePopup {
  constructor() {
    this.focusMode = {
      isActive: false,
      isBreak: false,
      sessionStartTime: null,
      sessionDuration: 25,
      breakDuration: 5,
      sessionsCompleted: 0
    };
    this.timer = null;
    this.isPaused = false;
    this.remainingTime = 25; // Default to 25 minutes
    this.totalTime = 25;
    this.connectionRetries = 0;
    this.maxRetries = 3;
    this.isConnected = false;
    this.init();
  }

  async init() {
    try {
      // Show loading state
      this.showLoading(true);

      // Load current focus mode status with retry logic
      await this.loadFocusStatusWithRetry();

      // Setup event listeners
      this.setupEventListeners();

      // Initialize UI with proper defaults
      this.initializeUI();

      // Update UI
      this.updateUI();

      // Start timer if session is active
      if (this.focusMode.isActive && this.isConnected) {
        this.startTimer();
      }

      // Hide loading state
      this.showLoading(false);

      // Show connection status
      this.updateConnectionStatus();

    } catch (error) {
      console.error('Focus mode initialization error:', error);
      this.showLoading(false);
      this.handleInitializationError(error);
    }
  }

  async loadFocusStatusWithRetry() {
    for (let attempt = 0; attempt < this.maxRetries; attempt++) {
      try {
        const response = await this.loadFocusStatus();
        if (response) {
          this.isConnected = true;
          this.connectionRetries = 0;
          return response;
        }
      } catch (error) {
        console.warn(`Connection attempt ${attempt + 1} failed:`, error);
        this.connectionRetries = attempt + 1;

        if (attempt < this.maxRetries - 1) {
          // Wait before retry (exponential backoff)
          await new Promise(resolve => setTimeout(resolve, Math.pow(2, attempt) * 1000));
        }
      }
    }

    // All retries failed
    this.isConnected = false;
    this.handleConnectionFailure();
    return null;
  }

  async loadFocusStatus() {
    try {
      // Add timeout to prevent hanging
      const timeoutPromise = new Promise((_, reject) =>
        setTimeout(() => reject(new Error('Connection timeout')), 5000)
      );

      const messagePromise = chrome.runtime.sendMessage({ action: 'getFocusStatus' });

      const response = await Promise.race([messagePromise, timeoutPromise]);

      if (response && response.focusMode) {
        this.focusMode = { ...this.focusMode, ...response.focusMode };

        // Calculate remaining time if session is active
        if (this.focusMode.isActive && this.focusMode.sessionStartTime) {
          const elapsed = (Date.now() - this.focusMode.sessionStartTime) / 1000 / 60; // minutes
          const sessionDuration = this.focusMode.isBreak ? this.focusMode.breakDuration : this.focusMode.sessionDuration;
          this.remainingTime = Math.max(0, sessionDuration - elapsed);
          this.totalTime = sessionDuration;
        } else {
          // Set default values for inactive session
          this.remainingTime = this.focusMode.sessionDuration;
          this.totalTime = this.focusMode.sessionDuration;
        }

        return response;
      } else {
        throw new Error('Invalid response from background script');
      }
    } catch (error) {
      console.error('Error loading focus status:', error);
      throw error;
    }
  }

  handleConnectionFailure() {
    console.warn('Failed to connect to background script, using offline mode');

    // Set default values for offline mode
    this.focusMode = {
      isActive: false,
      isBreak: false,
      sessionStartTime: null,
      sessionDuration: 25,
      breakDuration: 5,
      sessionsCompleted: 0
    };

    this.remainingTime = this.focusMode.sessionDuration;
    this.totalTime = this.focusMode.sessionDuration;

    // Show offline notification
    this.showNotification('Running in offline mode - some features may be limited', 'warning');
  }

  handleInitializationError(error) {
    console.error('Initialization failed:', error);

    // Set safe defaults
    this.remainingTime = 25;
    this.totalTime = 25;
    this.isConnected = false;

    // Update UI to show error state
    this.updateUI();
    this.showNotification('Failed to initialize focus mode', 'error');
  }

  initializeUI() {
    // Set initial timer display
    this.updateTimerDisplay();

    // Set initial progress
    this.updateProgress();

    // Load settings from local storage as fallback
    this.loadLocalSettings();
  }

  loadLocalSettings() {
    try {
      const savedSettings = localStorage.getItem('focusGuardSettings');
      if (savedSettings) {
        const settings = JSON.parse(savedSettings);
        if (settings.focusMode) {
          this.focusMode.sessionDuration = settings.focusMode.workDuration || 25;
          this.focusMode.breakDuration = settings.focusMode.breakDuration || 5;

          // Update UI with loaded settings
          if (!this.focusMode.isActive) {
            this.remainingTime = this.focusMode.sessionDuration;
            this.totalTime = this.focusMode.sessionDuration;
          }
        }
      }
    } catch (error) {
      console.warn('Failed to load local settings:', error);
    }
  }

  updateConnectionStatus() {
    // Add connection indicator to UI
    const header = document.querySelector('.popup-header');
    let statusIndicator = document.getElementById('connectionStatus');

    if (!statusIndicator) {
      statusIndicator = document.createElement('div');
      statusIndicator.id = 'connectionStatus';
      statusIndicator.style.cssText = `
        position: absolute;
        top: 8px;
        right: 50px;
        font-size: 10px;
        padding: 2px 6px;
        border-radius: 4px;
        font-weight: 500;
      `;
      header.appendChild(statusIndicator);
    }

    if (this.isConnected) {
      statusIndicator.textContent = '● Online';
      statusIndicator.style.background = 'rgba(72, 187, 120, 0.2)';
      statusIndicator.style.color = '#22543d';
    } else {
      statusIndicator.textContent = '● Offline';
      statusIndicator.style.background = 'rgba(245, 101, 101, 0.2)';
      statusIndicator.style.color = '#742a2a';
    }
  }

  setupEventListeners() {
    // Control buttons
    document.getElementById('startBtn').addEventListener('click', () => this.startSession());
    document.getElementById('pauseBtn').addEventListener('click', () => this.pauseSession());
    document.getElementById('stopBtn').addEventListener('click', () => this.stopSession());
    document.getElementById('closeBtn').addEventListener('click', () => window.close());

    // Settings
    document.getElementById('workDuration').addEventListener('change', (e) => {
      this.focusMode.sessionDuration = parseInt(e.target.value);
      this.updateSettings();
    });

    document.getElementById('breakDuration').addEventListener('change', (e) => {
      this.focusMode.breakDuration = parseInt(e.target.value);
      this.updateSettings();
    });

    document.getElementById('autoBreaksToggle').addEventListener('click', (e) => {
      e.target.classList.toggle('active');
      this.updateSettings();
    });
  }

  async startSession() {
    try {
      this.showLoading(true);

      if (this.isConnected) {
        // Try to start session through background script
        const response = await this.sendMessageWithRetry({ action: 'startFocusSession' });
        if (response && response.success) {
          this.focusMode = response.focusMode;
          this.remainingTime = this.focusMode.sessionDuration;
          this.totalTime = this.focusMode.sessionDuration;
          this.isPaused = false;

          this.updateUI();
          this.startTimer();
          this.showNotification('Focus session started!', 'success');

          // Save to local storage as backup
          this.saveLocalSession();
          return;
        }
      }

      // Fallback to local mode
      this.startLocalSession();

    } catch (error) {
      console.error('Error starting session:', error);
      this.startLocalSession();
    } finally {
      this.showLoading(false);
    }
  }

  startLocalSession() {
    console.log('Starting session in local mode');

    this.focusMode.isActive = true;
    this.focusMode.isBreak = false;
    this.focusMode.sessionStartTime = Date.now();
    this.remainingTime = this.focusMode.sessionDuration;
    this.totalTime = this.focusMode.sessionDuration;
    this.isPaused = false;

    this.updateUI();
    this.startTimer();
    this.saveLocalSession();
    this.showNotification('Focus session started (local mode)!', 'success');
  }

  async sendMessageWithRetry(message, retries = 2) {
    for (let attempt = 0; attempt < retries; attempt++) {
      try {
        const timeoutPromise = new Promise((_, reject) =>
          setTimeout(() => reject(new Error('Message timeout')), 3000)
        );

        const messagePromise = chrome.runtime.sendMessage(message);
        const response = await Promise.race([messagePromise, timeoutPromise]);

        if (response) {
          return response;
        }
      } catch (error) {
        console.warn(`Message attempt ${attempt + 1} failed:`, error);
        if (attempt < retries - 1) {
          await new Promise(resolve => setTimeout(resolve, 1000));
        }
      }
    }
    throw new Error('All message attempts failed');
  }

  saveLocalSession() {
    try {
      const sessionData = {
        focusMode: this.focusMode,
        remainingTime: this.remainingTime,
        totalTime: this.totalTime,
        isPaused: this.isPaused,
        timestamp: Date.now()
      };
      localStorage.setItem('focusGuardCurrentSession', JSON.stringify(sessionData));
    } catch (error) {
      console.warn('Failed to save local session:', error);
    }
  }

  loadLocalSession() {
    try {
      const sessionData = localStorage.getItem('focusGuardCurrentSession');
      if (sessionData) {
        const data = JSON.parse(sessionData);

        // Check if session is still valid (not older than 2 hours)
        if (Date.now() - data.timestamp < 2 * 60 * 60 * 1000) {
          this.focusMode = data.focusMode;
          this.remainingTime = data.remainingTime;
          this.totalTime = data.totalTime;
          this.isPaused = data.isPaused;

          // Recalculate remaining time if session was active
          if (this.focusMode.isActive && this.focusMode.sessionStartTime) {
            const elapsed = (Date.now() - this.focusMode.sessionStartTime) / 1000 / 60;
            const sessionDuration = this.focusMode.isBreak ? this.focusMode.breakDuration : this.focusMode.sessionDuration;
            this.remainingTime = Math.max(0, sessionDuration - elapsed);
          }

          return true;
        }
      }
    } catch (error) {
      console.warn('Failed to load local session:', error);
    }
    return false;
  }

  async pauseSession() {
    if (this.timer) {
      clearInterval(this.timer);
      this.timer = null;
      this.isPaused = true;
      this.updateUI();
      this.showNotification('Session paused', 'info');
    }
  }

  async resumeSession() {
    if (this.isPaused) {
      this.isPaused = false;
      this.startTimer();
      this.updateUI();
      this.showNotification('Session resumed', 'success');
    }
  }

  async stopSession() {
    try {
      this.showLoading(true);

      if (this.timer) {
        clearInterval(this.timer);
        this.timer = null;
      }

      if (this.isConnected) {
        try {
          const response = await this.sendMessageWithRetry({ action: 'stopFocusMode' });
          if (response && response.success) {
            this.focusMode = response.focusMode;
          }
        } catch (error) {
          console.warn('Failed to stop session via background script:', error);
        }
      }

      // Always update local state
      this.focusMode.isActive = false;
      this.focusMode.isBreak = false;
      this.focusMode.sessionStartTime = null;
      this.remainingTime = this.focusMode.sessionDuration;
      this.totalTime = this.focusMode.sessionDuration;
      this.isPaused = false;

      this.updateUI();
      this.clearLocalSession();
      this.showNotification('Focus session stopped', 'info');

    } catch (error) {
      console.error('Error stopping session:', error);
      this.showNotification('Failed to stop session', 'error');
    } finally {
      this.showLoading(false);
    }
  }

  clearLocalSession() {
    try {
      localStorage.removeItem('focusGuardCurrentSession');
    } catch (error) {
      console.warn('Failed to clear local session:', error);
    }
  }

  startTimer() {
    if (this.timer) {
      clearInterval(this.timer);
    }

    this.timer = setInterval(() => {
      this.remainingTime -= 1 / 60; // Decrease by 1 second (converted to minutes)

      if (this.remainingTime <= 0) {
        this.handleSessionEnd();
      } else {
        this.updateTimerDisplay();
        this.updateProgress();
      }
    }, 1000);
  }

  async handleSessionEnd() {
    if (this.timer) {
      clearInterval(this.timer);
      this.timer = null;
    }

    try {
      if (this.focusMode.isBreak) {
        // Break ended, stop focus mode
        await this.stopSession();
      } else {
        // Work session ended, start break
        const response = await chrome.runtime.sendMessage({ action: 'startBreak' });
        if (response && response.success) {
          this.focusMode = response.focusMode;
          this.remainingTime = this.focusMode.breakDuration;
          this.totalTime = this.focusMode.breakDuration;

          this.updateUI();
          this.startTimer();
          this.showNotification('Break time! Take a rest.', 'success');
        }
      }
    } catch (error) {
      console.error('Error handling session end:', error);
    }
  }

  updateUI() {
    this.updateTimerDisplay();
    this.updateProgress();
    this.updateControls();
    this.updateSessionInfo();
    this.updateSettings();
    this.updateVisualState();
  }

  updateTimerDisplay() {
    const timeText = document.getElementById('timeText');
    const sessionType = document.getElementById('sessionType');

    if (!timeText || !sessionType) {
      console.warn('Timer display elements not found');
      return;
    }

    // Ensure remainingTime is a valid number
    const safeRemainingTime = Math.max(0, this.remainingTime || 0);
    const minutes = Math.floor(safeRemainingTime);
    const seconds = Math.floor((safeRemainingTime - minutes) * 60);

    timeText.textContent = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;

    if (this.focusMode.isActive) {
      if (this.isPaused) {
        sessionType.textContent = this.focusMode.isBreak ? 'Break Paused' : 'Work Paused';
      } else {
        sessionType.textContent = this.focusMode.isBreak ? 'Break Time' : 'Work Session';
      }
    } else {
      sessionType.textContent = 'Ready to Focus';
    }
  }

  updateProgress() {
    const progressCircle = document.getElementById('progressCircle');

    if (!progressCircle) {
      console.warn('Progress circle element not found');
      return;
    }

    const circumference = 2 * Math.PI * 90; // radius = 90

    // Ensure we have valid values
    const safeTotalTime = Math.max(1, this.totalTime || 1); // Prevent division by zero
    const safeRemainingTime = Math.max(0, this.remainingTime || 0);

    const progress = Math.min(1, Math.max(0, (safeTotalTime - safeRemainingTime) / safeTotalTime));
    const offset = circumference - (progress * circumference);

    progressCircle.style.strokeDashoffset = offset;

    // Update progress circle color based on session type
    if (this.focusMode.isActive) {
      if (this.focusMode.isBreak) {
        progressCircle.style.stroke = '#f56565'; // Red for break
      } else {
        progressCircle.style.stroke = '#48bb78'; // Green for work
      }
    } else {
      progressCircle.style.stroke = '#667eea'; // Blue for ready state
    }
  }

  updateControls() {
    const startBtn = document.getElementById('startBtn');
    const pauseBtn = document.getElementById('pauseBtn');
    const stopBtn = document.getElementById('stopBtn');

    if (!startBtn || !pauseBtn || !stopBtn) {
      console.warn('Control button elements not found');
      return;
    }

    if (this.focusMode.isActive) {
      startBtn.classList.add('hidden');

      if (this.isPaused) {
        pauseBtn.innerHTML = '<span class="btn-icon">▶️</span>Resume';
        pauseBtn.onclick = () => this.resumeSession();
        pauseBtn.className = 'control-btn start-btn'; // Use green color for resume
      } else {
        pauseBtn.innerHTML = '<span class="btn-icon">⏸️</span>Pause';
        pauseBtn.onclick = () => this.pauseSession();
        pauseBtn.className = 'control-btn pause-btn'; // Use orange color for pause
      }

      pauseBtn.classList.remove('hidden');
      stopBtn.classList.remove('hidden');
    } else {
      startBtn.classList.remove('hidden');
      pauseBtn.classList.add('hidden');
      stopBtn.classList.add('hidden');
    }
  }

  updateSessionInfo() {
    document.getElementById('sessionsCount').textContent = this.focusMode.sessionsCompleted || 0;
    document.getElementById('currentSession').textContent = (this.focusMode.sessionsCompleted || 0) + 1;
    document.getElementById('nextBreak').textContent = `${this.focusMode.breakDuration} min`;
  }

  updateSettings() {
    document.getElementById('workDuration').value = this.focusMode.sessionDuration;
    document.getElementById('breakDuration').value = this.focusMode.breakDuration;

    // Update total time if not currently active
    if (!this.focusMode.isActive) {
      this.remainingTime = this.focusMode.sessionDuration;
      this.totalTime = this.focusMode.sessionDuration;
      this.updateTimerDisplay();
      this.updateProgress();
    }
  }

  updateVisualState() {
    const container = document.querySelector('.popup-container');

    // Remove all state classes
    container.classList.remove('active-session', 'break-mode', 'paused');

    if (this.focusMode.isActive) {
      if (this.isPaused) {
        container.classList.add('paused');
      } else if (this.focusMode.isBreak) {
        container.classList.add('break-mode');
      } else {
        container.classList.add('active-session');
      }
    }
  }

  async updateSettings() {
    // This would save settings to background script
    // For now, just update local state
  }

  showLoading(show) {
    const overlay = document.getElementById('loadingOverlay');
    if (show) {
      overlay.classList.add('show');
    } else {
      overlay.classList.remove('show');
    }
  }

  showNotification(message, type = 'info') {
    // Create a simple notification
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.textContent = message;

    notification.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      padding: 12px 20px;
      border-radius: 8px;
      color: white;
      font-weight: 500;
      z-index: 10000;
      animation: slideIn 0.3s ease;
      max-width: 300px;
    `;

    switch (type) {
      case 'success':
        notification.style.background = '#48bb78';
        break;
      case 'error':
        notification.style.background = '#f56565';
        break;
      case 'info':
      default:
        notification.style.background = '#667eea';
        break;
    }

    document.body.appendChild(notification);

    setTimeout(() => {
      if (notification.parentNode) {
        notification.parentNode.removeChild(notification);
      }
    }, 3000);
  }

  // Add focus tips rotation
  rotateFocusTips() {
    const tips = [
      "Remove distractions from your workspace and focus on one task at a time.",
      "Take deep breaths and set a clear intention for this work session.",
      "Turn off notifications and put your phone in another room.",
      "Break large tasks into smaller, manageable chunks.",
      "Use the two-minute rule: if it takes less than 2 minutes, do it now.",
      "Stay hydrated and maintain good posture while working.",
      "Celebrate small wins and progress made during each session."
    ];

    const tipElement = document.getElementById('focusTip');
    let currentTip = 0;

    setInterval(() => {
      currentTip = (currentTip + 1) % tips.length;
      tipElement.textContent = tips[currentTip];
    }, 30000); // Change tip every 30 seconds
  }
}

// Initialize focus mode when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  const focusMode = new FocusModePopup();
  focusMode.rotateFocusTips();
});
