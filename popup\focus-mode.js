// FocusGuard Pro - Focus Mode JavaScript

class FocusModePopup {
  constructor() {
    this.focusMode = {
      isActive: false,
      isBreak: false,
      sessionStartTime: null,
      sessionDuration: 25,
      breakDuration: 5,
      sessionsCompleted: 0
    };
    this.timer = null;
    this.isPaused = false;
    this.remainingTime = 0;
    this.totalTime = 0;
    this.init();
  }

  async init() {
    try {
      // Load current focus mode status
      await this.loadFocusStatus();
      
      // Setup event listeners
      this.setupEventListeners();
      
      // Update UI
      this.updateUI();
      
      // Start timer if session is active
      if (this.focusMode.isActive) {
        this.startTimer();
      }
    } catch (error) {
      console.error('Focus mode initialization error:', error);
    }
  }

  async loadFocusStatus() {
    try {
      const response = await chrome.runtime.sendMessage({ action: 'getFocusStatus' });
      if (response && response.focusMode) {
        this.focusMode = response.focusMode;
        
        // Calculate remaining time if session is active
        if (this.focusMode.isActive && this.focusMode.sessionStartTime) {
          const elapsed = (Date.now() - this.focusMode.sessionStartTime) / 1000 / 60; // minutes
          const sessionDuration = this.focusMode.isBreak ? this.focusMode.breakDuration : this.focusMode.sessionDuration;
          this.remainingTime = Math.max(0, sessionDuration - elapsed);
          this.totalTime = sessionDuration;
        }
      }
    } catch (error) {
      console.error('Error loading focus status:', error);
    }
  }

  setupEventListeners() {
    // Control buttons
    document.getElementById('startBtn').addEventListener('click', () => this.startSession());
    document.getElementById('pauseBtn').addEventListener('click', () => this.pauseSession());
    document.getElementById('stopBtn').addEventListener('click', () => this.stopSession());
    document.getElementById('closeBtn').addEventListener('click', () => window.close());

    // Settings
    document.getElementById('workDuration').addEventListener('change', (e) => {
      this.focusMode.sessionDuration = parseInt(e.target.value);
      this.updateSettings();
    });

    document.getElementById('breakDuration').addEventListener('change', (e) => {
      this.focusMode.breakDuration = parseInt(e.target.value);
      this.updateSettings();
    });

    document.getElementById('autoBreaksToggle').addEventListener('click', (e) => {
      e.target.classList.toggle('active');
      this.updateSettings();
    });
  }

  async startSession() {
    try {
      this.showLoading(true);
      
      const response = await chrome.runtime.sendMessage({ action: 'startFocusSession' });
      if (response && response.success) {
        this.focusMode = response.focusMode;
        this.remainingTime = this.focusMode.sessionDuration;
        this.totalTime = this.focusMode.sessionDuration;
        this.isPaused = false;
        
        this.updateUI();
        this.startTimer();
        this.showNotification('Focus session started!', 'success');
      }
    } catch (error) {
      console.error('Error starting session:', error);
      this.showNotification('Failed to start session', 'error');
    } finally {
      this.showLoading(false);
    }
  }

  async pauseSession() {
    if (this.timer) {
      clearInterval(this.timer);
      this.timer = null;
      this.isPaused = true;
      this.updateUI();
      this.showNotification('Session paused', 'info');
    }
  }

  async resumeSession() {
    if (this.isPaused) {
      this.isPaused = false;
      this.startTimer();
      this.updateUI();
      this.showNotification('Session resumed', 'success');
    }
  }

  async stopSession() {
    try {
      this.showLoading(true);
      
      if (this.timer) {
        clearInterval(this.timer);
        this.timer = null;
      }
      
      const response = await chrome.runtime.sendMessage({ action: 'stopFocusMode' });
      if (response && response.success) {
        this.focusMode = response.focusMode;
        this.remainingTime = 0;
        this.isPaused = false;
        
        this.updateUI();
        this.showNotification('Focus session stopped', 'info');
      }
    } catch (error) {
      console.error('Error stopping session:', error);
      this.showNotification('Failed to stop session', 'error');
    } finally {
      this.showLoading(false);
    }
  }

  startTimer() {
    if (this.timer) {
      clearInterval(this.timer);
    }

    this.timer = setInterval(() => {
      this.remainingTime -= 1/60; // Decrease by 1 second (converted to minutes)
      
      if (this.remainingTime <= 0) {
        this.handleSessionEnd();
      } else {
        this.updateTimerDisplay();
        this.updateProgress();
      }
    }, 1000);
  }

  async handleSessionEnd() {
    if (this.timer) {
      clearInterval(this.timer);
      this.timer = null;
    }

    try {
      if (this.focusMode.isBreak) {
        // Break ended, stop focus mode
        await this.stopSession();
      } else {
        // Work session ended, start break
        const response = await chrome.runtime.sendMessage({ action: 'startBreak' });
        if (response && response.success) {
          this.focusMode = response.focusMode;
          this.remainingTime = this.focusMode.breakDuration;
          this.totalTime = this.focusMode.breakDuration;
          
          this.updateUI();
          this.startTimer();
          this.showNotification('Break time! Take a rest.', 'success');
        }
      }
    } catch (error) {
      console.error('Error handling session end:', error);
    }
  }

  updateUI() {
    this.updateTimerDisplay();
    this.updateProgress();
    this.updateControls();
    this.updateSessionInfo();
    this.updateSettings();
    this.updateVisualState();
  }

  updateTimerDisplay() {
    const timeText = document.getElementById('timeText');
    const sessionType = document.getElementById('sessionType');
    
    const minutes = Math.floor(this.remainingTime);
    const seconds = Math.floor((this.remainingTime - minutes) * 60);
    
    timeText.textContent = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    
    if (this.focusMode.isActive) {
      sessionType.textContent = this.focusMode.isBreak ? 'Break Time' : 'Work Session';
    } else {
      sessionType.textContent = 'Ready to Focus';
    }
  }

  updateProgress() {
    const progressCircle = document.getElementById('progressCircle');
    const circumference = 2 * Math.PI * 90; // radius = 90
    
    if (this.totalTime > 0) {
      const progress = (this.totalTime - this.remainingTime) / this.totalTime;
      const offset = circumference - (progress * circumference);
      progressCircle.style.strokeDashoffset = offset;
    } else {
      progressCircle.style.strokeDashoffset = circumference;
    }
  }

  updateControls() {
    const startBtn = document.getElementById('startBtn');
    const pauseBtn = document.getElementById('pauseBtn');
    const stopBtn = document.getElementById('stopBtn');

    if (this.focusMode.isActive) {
      startBtn.classList.add('hidden');
      if (this.isPaused) {
        pauseBtn.textContent = '▶️ Resume';
        pauseBtn.onclick = () => this.resumeSession();
      } else {
        pauseBtn.textContent = '⏸️ Pause';
        pauseBtn.onclick = () => this.pauseSession();
      }
      pauseBtn.classList.remove('hidden');
      stopBtn.classList.remove('hidden');
    } else {
      startBtn.classList.remove('hidden');
      pauseBtn.classList.add('hidden');
      stopBtn.classList.add('hidden');
    }
  }

  updateSessionInfo() {
    document.getElementById('sessionsCount').textContent = this.focusMode.sessionsCompleted || 0;
    document.getElementById('currentSession').textContent = (this.focusMode.sessionsCompleted || 0) + 1;
    document.getElementById('nextBreak').textContent = `${this.focusMode.breakDuration} min`;
  }

  updateSettings() {
    document.getElementById('workDuration').value = this.focusMode.sessionDuration;
    document.getElementById('breakDuration').value = this.focusMode.breakDuration;
    
    // Update total time if not currently active
    if (!this.focusMode.isActive) {
      this.remainingTime = this.focusMode.sessionDuration;
      this.totalTime = this.focusMode.sessionDuration;
      this.updateTimerDisplay();
      this.updateProgress();
    }
  }

  updateVisualState() {
    const container = document.querySelector('.popup-container');
    
    // Remove all state classes
    container.classList.remove('active-session', 'break-mode', 'paused');
    
    if (this.focusMode.isActive) {
      if (this.isPaused) {
        container.classList.add('paused');
      } else if (this.focusMode.isBreak) {
        container.classList.add('break-mode');
      } else {
        container.classList.add('active-session');
      }
    }
  }

  async updateSettings() {
    // This would save settings to background script
    // For now, just update local state
  }

  showLoading(show) {
    const overlay = document.getElementById('loadingOverlay');
    if (show) {
      overlay.classList.add('show');
    } else {
      overlay.classList.remove('show');
    }
  }

  showNotification(message, type = 'info') {
    // Create a simple notification
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.textContent = message;
    
    notification.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      padding: 12px 20px;
      border-radius: 8px;
      color: white;
      font-weight: 500;
      z-index: 10000;
      animation: slideIn 0.3s ease;
      max-width: 300px;
    `;
    
    switch (type) {
      case 'success':
        notification.style.background = '#48bb78';
        break;
      case 'error':
        notification.style.background = '#f56565';
        break;
      case 'info':
      default:
        notification.style.background = '#667eea';
        break;
    }
    
    document.body.appendChild(notification);
    
    setTimeout(() => {
      if (notification.parentNode) {
        notification.parentNode.removeChild(notification);
      }
    }, 3000);
  }

  // Add focus tips rotation
  rotateFocusTips() {
    const tips = [
      "Remove distractions from your workspace and focus on one task at a time.",
      "Take deep breaths and set a clear intention for this work session.",
      "Turn off notifications and put your phone in another room.",
      "Break large tasks into smaller, manageable chunks.",
      "Use the two-minute rule: if it takes less than 2 minutes, do it now.",
      "Stay hydrated and maintain good posture while working.",
      "Celebrate small wins and progress made during each session."
    ];
    
    const tipElement = document.getElementById('focusTip');
    let currentTip = 0;
    
    setInterval(() => {
      currentTip = (currentTip + 1) % tips.length;
      tipElement.textContent = tips[currentTip];
    }, 30000); // Change tip every 30 seconds
  }
}

// Initialize focus mode when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  const focusMode = new FocusModePopup();
  focusMode.rotateFocusTips();
});
