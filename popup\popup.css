/* FocusGuard Pro - Popup Styles */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    background: #ffffff;
    color: #2d3748;
    width: 380px;
    min-height: 500px;
    overflow-x: hidden;
}

.popup-container {
    position: relative;
    height: 100%;
}

/* Header */
.popup-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 16px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo {
    display: flex;
    align-items: center;
    gap: 8px;
}

.shield-icon {
    width: 24px;
    height: 24px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
}

.logo h1 {
    font-size: 16px;
    font-weight: 600;
}

.status-indicator {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 12px;
}

.status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #48bb78;
    animation: pulse 2s infinite;
}

.status-dot.inactive {
    background: #f56565;
    animation: none;
}

@keyframes pulse {

    0%,
    100% {
        opacity: 1;
    }

    50% {
        opacity: 0.5;
    }
}

/* Main Toggle */
.main-toggle {
    padding: 20px;
    border-bottom: 1px solid #edf2f7;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.toggle-info h3 {
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 2px;
}

.toggle-info p {
    font-size: 12px;
    color: #718096;
}

.toggle-switch {
    position: relative;
    width: 48px;
    height: 24px;
    background: #e2e8f0;
    border-radius: 12px;
    cursor: pointer;
    transition: background 0.3s ease;
}

.toggle-switch.active {
    background: #667eea;
}

.toggle-switch::after {
    content: '';
    position: absolute;
    top: 2px;
    left: 2px;
    width: 20px;
    height: 20px;
    background: white;
    border-radius: 50%;
    transition: transform 0.3s ease;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.toggle-switch.active::after {
    transform: translateX(24px);
}

/* Quick Actions */
.quick-actions {
    padding: 20px;
    border-bottom: 1px solid #edf2f7;
}

.quick-actions h3 {
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 16px;
}

.action-section {
    margin-bottom: 16px;
}

.action-section:last-child {
    margin-bottom: 0;
}

.input-group {
    display: flex;
    gap: 8px;
}

.site-input {
    flex: 1;
    padding: 8px 12px;
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    font-size: 13px;
    transition: border-color 0.2s ease;
}

.site-input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.btn {
    padding: 8px 12px;
    border: none;
    border-radius: 6px;
    font-size: 12px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 4px;
    white-space: nowrap;
}

.btn-primary {
    background: #667eea;
    color: white;
}

.btn-primary:hover {
    background: #5a67d8;
    transform: translateY(-1px);
}

.btn-secondary {
    background: #f7fafc;
    color: #718096;
    border: 1px solid #e2e8f0;
}

.btn-secondary:hover {
    background: #edf2f7;
    border-color: #cbd5e1;
}

.btn-accent {
    background: #48bb78;
    color: white;
}

.btn-accent:hover {
    background: #38a169;
}

.btn-ghost {
    background: transparent;
    color: #718096;
}

.btn-ghost:hover {
    background: #f7fafc;
}

.btn-icon {
    font-size: 10px;
}

/* Current Site */
.current-site {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px;
    background: #f7fafc;
    border-radius: 8px;
    border: 1px solid #edf2f7;
}

.site-info {
    display: flex;
    align-items: center;
    gap: 10px;
    flex: 1;
}

.site-icon {
    width: 32px;
    height: 32px;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    background: #667eea;
    color: white;
}

.site-details h4 {
    font-size: 13px;
    font-weight: 500;
    margin-bottom: 2px;
}

.site-details p {
    font-size: 11px;
    color: #718096;
}

/* Blocked Sites */
.blocked-sites {
    padding: 20px;
    max-height: 200px;
    overflow-y: auto;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
}

.section-header h3 {
    font-size: 14px;
    font-weight: 600;
}

.site-count {
    font-size: 11px;
    color: #718096;
    background: #f7fafc;
    padding: 2px 6px;
    border-radius: 4px;
}

.sites-list {
    max-height: 150px;
    overflow-y: auto;
}

.site-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #f7fafc;
}

.site-item:last-child {
    border-bottom: none;
}

.site-item .site-info {
    gap: 8px;
}

.site-item .site-icon {
    width: 24px;
    height: 24px;
    font-size: 10px;
}

.site-item .site-details h4 {
    font-size: 12px;
}

.site-item .site-details p {
    font-size: 10px;
}

.remove-btn {
    padding: 4px 6px;
    background: #fed7d7;
    color: #c53030;
    border: none;
    border-radius: 4px;
    font-size: 10px;
    cursor: pointer;
    transition: background 0.2s ease;
}

.remove-btn:hover {
    background: #feb2b2;
}

.empty-state {
    text-align: center;
    padding: 24px 12px;
    color: #718096;
}

.empty-icon {
    font-size: 24px;
    margin-bottom: 8px;
}

.empty-state p {
    font-size: 13px;
    margin-bottom: 4px;
}

.empty-state small {
    font-size: 11px;
    color: #a0aec0;
}

/* Footer Actions */
.footer-actions {
    padding: 16px 20px;
    border-top: 1px solid #edf2f7;
    display: flex;
    gap: 8px;
}

.footer-actions .btn {
    flex: 1;
    justify-content: center;
}

/* Loading Overlay */
.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.9);
    display: none;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.loading-overlay.show {
    display: flex;
}

.loading-spinner {
    width: 24px;
    height: 24px;
    border: 2px solid #e2e8f0;
    border-top: 2px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 8px;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

.loading-overlay p {
    font-size: 12px;
    color: #718096;
}

/* Scrollbar Styling */
.sites-list::-webkit-scrollbar {
    width: 4px;
}

.sites-list::-webkit-scrollbar-track {
    background: #f7fafc;
}

.sites-list::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 2px;
}

.sites-list::-webkit-scrollbar-thumb:hover {
    background: #a0aec0;
}