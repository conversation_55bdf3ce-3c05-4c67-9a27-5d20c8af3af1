<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FocusGuard Pro - Settings</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 16px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
            display: flex;
        }

        .sidebar {
            width: 220px;
            background: #fafbfc;
            padding: 24px 0;
            border-right: 1px solid #eef2f6;
        }

        .logo {
            padding: 0 20px 24px;
            border-bottom: 1px solid #eef2f6;
            margin-bottom: 16px;
        }

        .logo h1 {
            font-size: 18px;
            font-weight: 600;
            color: #2d3748;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .shield-icon {
            width: 20px;
            height: 20px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 10px;
        }

        .nav-menu {
            list-style: none;
        }

        .nav-item {
            margin-bottom: 2px;
        }

        .nav-link {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 10px 20px;
            color: #718096;
            text-decoration: none;
            font-size: 13px;
            font-weight: 500;
            transition: all 0.15s ease;
            border-radius: 0 20px 20px 0;
            margin-right: 12px;
        }

        .nav-link:hover {
            background: rgba(102, 126, 234, 0.08);
            color: #667eea;
            transform: translateX(2px);
        }

        .nav-link.active {
            background: rgba(102, 126, 234, 0.12);
            color: #667eea;
            border-right: none;
        }

        .nav-icon {
            width: 16px;
            height: 16px;
            opacity: 0.8;
        }

        .main-content {
            flex: 1;
            padding: 32px;
            overflow-y: auto;
            max-height: 90vh;
        }

        .page-header {
            margin-bottom: 24px;
        }

        .page-title {
            font-size: 24px;
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 6px;
        }

        .page-subtitle {
            color: #718096;
            font-size: 15px;
        }

        .settings-section {
            background: white;
            border: 1px solid #edf2f7;
            border-radius: 8px;
            margin-bottom: 16px;
            overflow: hidden;
            box-shadow: 0 1px 3px rgba(0,0,0,0.04);
        }

        .section-header {
            padding: 16px 20px;
            border-bottom: 1px solid #edf2f7;
            background: #fafbfc;
        }

        .section-title {
            font-size: 16px;
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 2px;
        }

        .section-description {
            color: #718096;
            font-size: 13px;
        }

        .section-content {
            padding: 20px;
        }

        .setting-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid #f7fafc;
        }

        .setting-item:last-child {
            border-bottom: none;
        }

        .setting-info h4 {
            font-size: 14px;
            font-weight: 500;
            color: #2d3748;
            margin-bottom: 2px;
        }

        .setting-info p {
            color: #718096;
            font-size: 13px;
        }

        .toggle-switch {
            position: relative;
            width: 44px;
            height: 22px;
            background: #e2e8f0;
            border-radius: 11px;
            cursor: pointer;
            transition: background 0.2s ease;
        }

        .toggle-switch.active {
            background: #667eea;
        }

        .toggle-switch::after {
            content: '';
            position: absolute;
            top: 2px;
            left: 2px;
            width: 18px;
            height: 18px;
            background: white;
            border-radius: 50%;
            transition: transform 0.2s ease;
            box-shadow: 0 1px 3px rgba(0,0,0,0.2);
        }

        .toggle-switch.active::after {
            transform: translateX(22px);
        }

        .input-group {
            margin-bottom: 16px;
        }

        .input-label {
            display: block;
            font-size: 13px;
            font-weight: 500;
            color: #4a5568;
            margin-bottom: 6px;
        }

        .input-field {
            width: 100%;
            padding: 10px 12px;
            border: 1px solid #e2e8f0;
            border-radius: 6px;
            font-size: 13px;
            transition: border-color 0.2s ease;
            background: #fafbfc;
        }

        .input-field:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.08);
            background: white;
        }

        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 6px;
            font-size: 13px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .btn-primary {
            background: #667eea;
            color: white;
        }

        .btn-primary:hover {
            background: #5a67d8;
            transform: translateY(-1px);
        }

        .btn-secondary {
            background: #f7fafc;
            color: #718096;
            border: 1px solid #e2e8f0;
        }

        .btn-secondary:hover {
            background: #edf2f7;
            border-color: #cbd5e1;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(160px, 1fr));
            gap: 12px;
            margin-bottom: 20px;
        }

        .stat-card {
            background: #fafbfc;
            padding: 16px;
            border-radius: 8px;
            border: 1px solid #edf2f7;
            text-align: center;
        }

        .stat-number {
            font-size: 20px;
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 4px;
        }

        .stat-label {
            color: #718096;
            font-size: 12px;
            font-weight: 500;
        }

        .site-list {
            max-height: 240px;
            overflow-y: auto;
        }

        .site-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 10px 0;
            border-bottom: 1px solid #f7fafc;
        }

        .site-info {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .site-icon {
            width: 28px;
            height: 28px;
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 11px;
            color: white;
        }

        .site-details h5 {
            font-size: 13px;
            font-weight: 500;
            color: #2d3748;
            margin-bottom: 1px;
        }

        .site-details p {
            font-size: 11px;
            color: #718096;
        }

        .time-badge {
            background: #f7fafc;
            color: #718096;
            padding: 3px 6px;
            border-radius: 4px;
            font-size: 11px;
            font-weight: 500;
        }

        .danger-zone {
            border-color: #fecaca;
            background: #fef2f2;
        }

        .danger-zone .section-header {
            background: #fef2f2;
            border-color: #fecaca;
        }

        .danger-zone .section-title {
            color: #dc2626;
        }

        .btn-danger {
            background: #dc2626;
            color: white;
        }

        .btn-danger:hover {
            background: #b91c1c;
        }
    </style>
</head>
<body>
    <div class="container">
        <nav class="sidebar">
            <div class="logo">
                <h1>
                    <div class="shield-icon">🛡️</div>
                    FocusGuard Pro
                </h1>
            </div>
            
            <ul class="nav-menu">
                <li class="nav-item">
                    <a href="#overview" class="nav-link active">
                        <span class="nav-icon">📊</span>
                        Overview
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#blocking" class="nav-link">
                        <span class="nav-icon">🚫</span>
                        Block List
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#limits" class="nav-link">
                        <span class="nav-icon">⏱️</span>
                        Time Limits
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#focus" class="nav-link">
                        <span class="nav-icon">🎯</span>
                        Focus Mode
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#schedule" class="nav-link">
                        <span class="nav-icon">📅</span>
                        Schedule
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#keywords" class="nav-link">
                        <span class="nav-icon">🔍</span>
                        Keyword Blocking
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#adult" class="nav-link">
                        <span class="nav-icon">🔐</span>
                        Adult Content
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#password" class="nav-link">
                        <span class="nav-icon">🔑</span>
                        Password
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#analytics" class="nav-link">
                        <span class="nav-icon">📈</span>
                        Analytics
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#general" class="nav-link">
                        <span class="nav-icon">⚙️</span>
                        General
                    </a>
                </li>
            </ul>
        </nav>

        <main class="main-content">
            <div class="page-header">
                <h1 class="page-title">Settings</h1>
                <p class="page-subtitle">Customize your FocusGuard Pro experience</p>
            </div>

            <!-- Overview Section -->
            <section class="settings-section">
                <div class="section-header">
                    <h2 class="section-title">Overview</h2>
                    <p class="section-description">Your productivity statistics at a glance</p>
                </div>
                <div class="section-content">
                    <div class="stats-grid">
                        <div class="stat-card">
                            <div class="stat-number">156</div>
                            <div class="stat-label">Sites Blocked</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number">8.2h</div>
                            <div class="stat-label">Time Saved Today</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number">23</div>
                            <div class="stat-label">Focus Sessions</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number">94%</div>
                            <div class="stat-label">Success Rate</div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Block List Section -->
            <section class="settings-section">
                <div class="section-header">
                    <h2 class="section-title">Block List</h2>
                    <p class="section-description">Manage websites you want to block</p>
                </div>
                <div class="section-content">
                    <div class="input-group">
                        <label class="input-label">Add Website</label>
                        <div style="display: flex; gap: 10px;">
                            <input type="text" class="input-field" placeholder="Enter website URL (e.g., facebook.com)">
                            <button class="btn btn-primary">Add Site</button>
                        </div>
                    </div>
                    
                    <div class="site-list">
                        <div class="site-item">
                            <div class="site-info">
                                <div class="site-icon" style="background: #1DA1F2;">𝕏</div>
                                <div class="site-details">
                                    <h5>twitter.com</h5>
                                    <p>Social Media</p>
                                </div>
                            </div>
                            <div class="time-badge">Permanently blocked</div>
                        </div>
                        <div class="site-item">
                            <div class="site-info">
                                <div class="site-icon" style="background: #1877F2;">f</div>
                                <div class="site-details">
                                    <h5>facebook.com</h5>
                                    <p>Social Media</p>
                                </div>
                            </div>
                            <div class="time-badge">2h left</div>
                        </div>
                        <div class="site-item">
                            <div class="site-info">
                                <div class="site-icon" style="background: #FF0000;">▶</div>
                                <div class="site-details">
                                    <h5>youtube.com</h5>
                                    <p>Entertainment</p>
                                </div>
                            </div>
                            <div class="time-badge">5 days left</div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Time Limits Section -->
            <section class="settings-section">
                <div class="section-header">
                    <h2 class="section-title">Time Limits</h2>
                    <p class="section-description">Set daily usage limits for websites</p>
                </div>
                <div class="section-content">
                    <div class="setting-item">
                        <div class="setting-info">
                            <h4>Enable Time Limits</h4>
                            <p>Automatically block sites after daily limit is reached</p>
                        </div>
                        <div class="toggle-switch active"></div>
                    </div>
                    
                    <div class="input-group">
                        <label class="input-label">Default Daily Limit</label>
                        <select class="input-field">
                            <option>30 minutes</option>
                            <option>1 hour</option>
                            <option selected>2 hours</option>
                            <option>3 hours</option>
                            <option>No limit</option>
                        </select>
                    </div>
                </div>
            </section>

            <!-- Focus Mode Section -->
            <section class="settings-section">
                <div class="section-header">
                    <h2 class="section-title">Focus Mode</h2>
                    <p class="section-description">Configure your Pomodoro timer settings</p>
                </div>
                <div class="section-content">
                    <div class="input-group">
                        <label class="input-label">Work Session Duration</label>
                        <select class="input-field">
                            <option>15 minutes</option>
                            <option>20 minutes</option>
                            <option selected>25 minutes</option>
                            <option>30 minutes</option>
                            <option>45 minutes</option>
                        </select>
                    </div>
                    
                    <div class="input-group">
                        <label class="input-label">Break Duration</label>
                        <select class="input-field">
                            <option selected>5 minutes</option>
                            <option>10 minutes</option>
                            <option>15 minutes</option>
                        </select>
                    </div>
                    
                    <div class="setting-item">
                        <div class="setting-info">
                            <h4>Auto-start breaks</h4>
                            <p>Automatically start break timer when work session ends</p>
                        </div>
                        <div class="toggle-switch active"></div>
                    </div>
                </div>
            </section>

            <!-- Keyword Blocking Section -->
            <section class="settings-section">
                <div class="section-header">
                    <h2 class="section-title">Keyword Blocking</h2>
                    <p class="section-description">Block searches containing specific words</p>
                </div>
                <div class="section-content">
                    <div class="setting-item">
                        <div class="setting-info">
                            <h4>Enable Keyword Blocking</h4>
                            <p>Block searches and content containing blocked keywords</p>
                        </div>
                        <div class="toggle-switch active"></div>
                    </div>
                    
                    <div class="input-group">
                        <label class="input-label">Blocked Keywords</label>
                        <textarea class="input-field" rows="4" placeholder="Enter keywords separated by commas (e.g., adult, porn, xxx)">adult, porn, xxx, sex, nude</textarea>
                    </div>
                </div>
            </section>

            <!-- Adult Content Section -->
            <section class="settings-section">
                <div class="section-header">
                    <h2 class="section-title">Adult Content Protection</h2>
                    <p class="section-description">Automatically block inappropriate content</p>
                </div>
                <div class="section-content">
                    <div class="setting-item">
                        <div class="setting-info">
                            <h4>Enable Adult Content Blocking</h4>
                            <p>Block all known adult websites and content</p>
                        </div>
                        <div class="toggle-switch active"></div>
                    </div>
                    
                    <div class="setting-item">
                        <div class="setting-info">
                            <h4>Safe Search</h4>
                            <p>Force safe search on all search engines</p>
                        </div>
                        <div class="toggle-switch active"></div>
                    </div>
                </div>
            </section>

            <!-- Password Section -->
            <section class="settings-section">
                <div class="section-header">
                    <h2 class="section-title">Master Password</h2>
                    <p class="section-description">Secure your settings with a password</p>
                </div>
                <div class="section-content">
                    <div class="setting-item">
                        <div class="setting-info">
                            <h4>Password Protection</h4>
                            <p>Require password to modify settings and unblock sites</p>
                        </div>
                        <div class="toggle-switch active"></div>
                    </div>
                    
                    <div class="input-group">
                        <label class="input-label">Change Password</label>
                        <input type="password" class="input-field" placeholder="Enter new password">
                    </div>
                    
                    <button class="btn btn-primary">Update Password</button>
                </div>
            </section>

            <!-- Analytics Section -->
            <section class="settings-section">
                <div class="section-header">
                    <h2 class="section-title">Analytics & Reports</h2>
                    <p class="section-description">Track your productivity and usage patterns</p>
                </div>
                <div class="section-content">
                    <div class="setting-item">
                        <div class="setting-info">
                            <h4>Enable Analytics</h4>
                            <p>Track time spent on websites and blocking statistics</p>
                        </div>
                        <div class="toggle-switch active"></div>
                    </div>
                    
                    <div class="setting-item">
                        <div class="setting-info">
                            <h4>Weekly Reports</h4>
                            <p>Receive weekly productivity reports via email</p>
                        </div>
                        <div class="toggle-switch"></div>
                    </div>
                    
                    <button class="btn btn-secondary" style="margin-top: 16px;">Export Data</button>
                </div>
            </section>

            <!-- General Settings Section -->
            <section class="settings-section">
                <div class="section-header">
                    <h2 class="section-title">General Settings</h2>
                    <p class="section-description">Configure general extension behavior</p>
                </div>
                <div class="section-content">
                    <div class="setting-item">
                        <div class="setting-info">
                            <h4>Work in Incognito Mode</h4>
                            <p>Apply blocking rules in private browsing mode</p>
                        </div>
                        <div class="toggle-switch active"></div>
                    </div>
                    
                    <div class="setting-item">
                        <div class="setting-info">
                            <h4>Show Notifications</h4>
                            <p>Display notifications for blocked sites and timer alerts</p>
                        </div>
                        <div class="toggle-switch active"></div>
                    </div>
                    
                    <div class="input-group">
                        <label class="input-label">Default Block Duration</label>
                        <select class="input-field">
                            <option>1 hour</option>
                            <option>24 hours</option>
                            <option selected>5 days</option>
                            <option>1 week</option>
                            <option>Permanent</option>
                        </select>
                    </div>
                </div>
            </section>

            <!-- Danger Zone -->
            <section class="settings-section danger-zone">
                <div class="section-header">
                    <h2 class="section-title">Danger Zone</h2>
                    <p class="section-description">Irreversible and destructive actions</p>
                </div>
                <div class="section-content">
                    <div class="setting-item">
                        <div class="setting-info">
                            <h4>Reset All Settings</h4>
                            <p>This will reset all your settings to default values</p>
                        </div>
                        <button class="btn btn-secondary">Reset Settings</button>
                    </div>
                    
                    <div class="setting-item">
                        <div class="setting-info">
                            <h4>Clear All Data</h4>
                            <p>Permanently delete all blocking data and statistics</p>
                        </div>
                        <button class="btn btn-danger">Clear Data</button>
                    </div>
                </div>
            </section>
        </main>
    </div>

    <script>
        // Toggle switches functionality
        document.querySelectorAll('.toggle-switch').forEach(toggle => {
            toggle.addEventListener('click', function() {
                this.classList.toggle('active');
            });
        });

        // Navigation functionality
        document.querySelectorAll('.nav-link').forEach(link => {
            link.addEventListener('click', function(e) {
                e.preventDefault();
                document.querySelectorAll('.nav-link').forEach(l => l.classList.remove('active'));
                this.classList.add('active');
            });
        });

        // Smooth scrolling for better UX
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });
    </script>
</body>
</html>