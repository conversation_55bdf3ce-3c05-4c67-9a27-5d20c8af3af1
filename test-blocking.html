<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FocusGuard Pro - Blocking Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .test-section h2 {
            color: #667eea;
            margin-bottom: 15px;
        }
        .step {
            margin-bottom: 15px;
            padding: 10px;
            background: #f8f9fa;
            border-left: 4px solid #667eea;
        }
        .step strong {
            color: #333;
        }
        .warning {
            background: #fff3cd;
            border-color: #ffeaa7;
            color: #856404;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .success {
            background: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .test-links {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }
        .test-link {
            padding: 15px;
            background: #667eea;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            text-align: center;
            transition: background 0.3s;
        }
        .test-link:hover {
            background: #5a67d8;
        }
        .debug-info {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin-top: 20px;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🛡️ FocusGuard Pro - Blocking Test Page</h1>
        
        <div class="warning">
            <strong>⚠️ Important:</strong> This page helps you test if the FocusGuard Pro extension is working correctly. Follow the steps below to verify blocking functionality.
        </div>

        <div class="test-section">
            <h2>📋 Step-by-Step Testing Instructions</h2>
            
            <div class="step">
                <strong>Step 1:</strong> Make sure FocusGuard Pro extension is installed and enabled in Chrome.
            </div>
            
            <div class="step">
                <strong>Step 2:</strong> Click the FocusGuard Pro icon in your browser toolbar.
            </div>
            
            <div class="step">
                <strong>Step 3:</strong> Add a test site to block (e.g., "example.com" or "facebook.com").
            </div>
            
            <div class="step">
                <strong>Step 4:</strong> Click one of the test links below to verify blocking works.
            </div>
            
            <div class="step">
                <strong>Step 5:</strong> If blocking works, you should see the FocusGuard Pro blocked page instead of the actual website.
            </div>
        </div>

        <div class="test-section">
            <h2>🔗 Test Links</h2>
            <p>Click these links to test if blocking is working (make sure to add these domains to your blocked list first):</p>
            
            <div class="test-links">
                <a href="https://example.com" class="test-link" target="_blank">
                    Test example.com
                </a>
                <a href="https://facebook.com" class="test-link" target="_blank">
                    Test facebook.com
                </a>
                <a href="https://twitter.com" class="test-link" target="_blank">
                    Test twitter.com
                </a>
                <a href="https://youtube.com" class="test-link" target="_blank">
                    Test youtube.com
                </a>
                <a href="https://reddit.com" class="test-link" target="_blank">
                    Test reddit.com
                </a>
                <a href="https://instagram.com" class="test-link" target="_blank">
                    Test instagram.com
                </a>
            </div>
        </div>

        <div class="test-section">
            <h2>🔧 Troubleshooting</h2>
            
            <div class="step">
                <strong>If blocking isn't working:</strong>
                <ul style="margin-top: 10px;">
                    <li>Check that the extension is enabled (not grayed out)</li>
                    <li>Verify the site is in your blocked list</li>
                    <li>Try refreshing the page after adding a site</li>
                    <li>Check the browser console for error messages (F12)</li>
                    <li>Make sure you're not in incognito mode (unless enabled in settings)</li>
                </ul>
            </div>
        </div>

        <div class="test-section">
            <h2>📊 Debug Information</h2>
            <p>Open browser console (F12) to see debug information when testing blocking.</p>
            
            <div class="debug-info" id="debugInfo">
                <div>Current URL: <span id="currentUrl"></span></div>
                <div>User Agent: <span id="userAgent"></span></div>
                <div>Extension Context: <span id="extensionContext"></span></div>
                <div>Test Time: <span id="testTime"></span></div>
            </div>
        </div>

        <div class="success">
            <strong>✅ Expected Behavior:</strong> When you click a test link for a blocked site, you should be redirected to the FocusGuard Pro blocked page with productivity tips and unblock options.
        </div>
    </div>

    <script>
        // Populate debug information
        document.getElementById('currentUrl').textContent = window.location.href;
        document.getElementById('userAgent').textContent = navigator.userAgent;
        document.getElementById('extensionContext').textContent = typeof chrome !== 'undefined' && chrome.runtime ? 'Available' : 'Not Available';
        document.getElementById('testTime').textContent = new Date().toLocaleString();

        // Log test page load
        console.log('FocusGuard Pro Test Page Loaded');
        console.log('Current URL:', window.location.href);
        console.log('Chrome Extension API Available:', typeof chrome !== 'undefined' && chrome.runtime);
        
        // Test extension communication
        if (typeof chrome !== 'undefined' && chrome.runtime) {
            chrome.runtime.sendMessage({ action: 'getBlockedSites' }, (response) => {
                console.log('Extension Response:', response);
                if (response) {
                    console.log('Blocked Sites:', response.sites);
                    console.log('Extension Enabled:', response.isEnabled);
                } else {
                    console.log('No response from extension - check if it\'s installed and enabled');
                }
            });
        }
    </script>
</body>
</html>
