<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Focus Mode Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        #log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <h1>FocusGuard Pro - Focus Mode Test</h1>
    
    <div class="test-section">
        <h2>Extension Status</h2>
        <div id="extensionStatus" class="status info">Checking extension...</div>
        <button onclick="checkExtension()">Check Extension</button>
    </div>

    <div class="test-section">
        <h2>Focus Mode Tests</h2>
        <div id="focusStatus" class="status info">Ready to test</div>
        <button onclick="testFocusMode()">Open Focus Mode</button>
        <button onclick="testStartSession()">Test Start Session</button>
        <button onclick="testStopSession()">Test Stop Session</button>
        <button onclick="testGetStatus()">Get Focus Status</button>
    </div>

    <div class="test-section">
        <h2>Test Log</h2>
        <div id="log"></div>
        <button onclick="clearLog()">Clear Log</button>
    </div>

    <script>
        function log(message, type = 'info') {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.innerHTML = `<span style="color: #666;">[${timestamp}]</span> ${message}`;
            logDiv.appendChild(logEntry);
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }

        async function checkExtension() {
            try {
                if (typeof chrome !== 'undefined' && chrome.runtime) {
                    log('Chrome extension API available', 'success');
                    
                    // Test basic communication
                    const response = await chrome.runtime.sendMessage({ action: 'getFocusStatus' });
                    if (response) {
                        log('Extension communication successful', 'success');
                        document.getElementById('extensionStatus').textContent = 'Extension is working!';
                        document.getElementById('extensionStatus').className = 'status success';
                    } else {
                        throw new Error('No response from extension');
                    }
                } else {
                    throw new Error('Chrome extension API not available');
                }
            } catch (error) {
                log(`Extension check failed: ${error.message}`, 'error');
                document.getElementById('extensionStatus').textContent = 'Extension not working';
                document.getElementById('extensionStatus').className = 'status error';
            }
        }

        function testFocusMode() {
            try {
                if (typeof chrome !== 'undefined' && chrome.windows) {
                    chrome.windows.create({
                        url: chrome.runtime.getURL('popup/focus-mode.html'),
                        type: 'popup',
                        width: 450,
                        height: 650,
                        focused: true
                    });
                    log('Focus mode window opened', 'success');
                } else {
                    // Fallback for testing
                    window.open('popup/focus-mode.html', 'focusMode', 'width=450,height=650');
                    log('Focus mode opened in new window (fallback)', 'info');
                }
            } catch (error) {
                log(`Failed to open focus mode: ${error.message}`, 'error');
            }
        }

        async function testStartSession() {
            try {
                const response = await chrome.runtime.sendMessage({ action: 'startFocusSession' });
                if (response && response.success) {
                    log('Focus session started successfully', 'success');
                    document.getElementById('focusStatus').textContent = 'Session active';
                    document.getElementById('focusStatus').className = 'status success';
                } else {
                    throw new Error('Failed to start session');
                }
            } catch (error) {
                log(`Start session failed: ${error.message}`, 'error');
            }
        }

        async function testStopSession() {
            try {
                const response = await chrome.runtime.sendMessage({ action: 'stopFocusMode' });
                if (response && response.success) {
                    log('Focus session stopped successfully', 'success');
                    document.getElementById('focusStatus').textContent = 'Session stopped';
                    document.getElementById('focusStatus').className = 'status info';
                } else {
                    throw new Error('Failed to stop session');
                }
            } catch (error) {
                log(`Stop session failed: ${error.message}`, 'error');
            }
        }

        async function testGetStatus() {
            try {
                const response = await chrome.runtime.sendMessage({ action: 'getFocusStatus' });
                if (response && response.focusMode) {
                    log(`Focus status: ${JSON.stringify(response.focusMode, null, 2)}`, 'info');
                    const isActive = response.focusMode.isActive;
                    document.getElementById('focusStatus').textContent = isActive ? 'Session active' : 'Session inactive';
                    document.getElementById('focusStatus').className = isActive ? 'status success' : 'status info';
                } else {
                    throw new Error('Invalid response');
                }
            } catch (error) {
                log(`Get status failed: ${error.message}`, 'error');
            }
        }

        // Auto-check extension on load
        window.addEventListener('load', () => {
            log('Test page loaded', 'info');
            setTimeout(checkExtension, 1000);
        });
    </script>
</body>
</html>
