<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Focus Mode Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f0f0f0;
        }
        .test-container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .test-btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 0;
        }
        .test-btn:hover {
            background: #5a67d8;
        }
        .status {
            margin: 20px 0;
            padding: 10px;
            border-radius: 4px;
            background: #e6fffa;
            border: 1px solid #81e6d9;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🛡️ FocusGuard Pro - Focus Mode Test</h1>
        <p>This page tests the focus mode modal functionality.</p>
        
        <div class="status" id="status">
            Ready to test focus mode...
        </div>
        
        <button class="test-btn" onclick="testFocusMode()">
            🎯 Open Focus Mode Modal
        </button>
        
        <button class="test-btn" onclick="testTimer()">
            ⏱️ Test Timer Functions
        </button>
        
        <button class="test-btn" onclick="testSettings()">
            ⚙️ Test Settings
        </button>
        
        <div id="test-results"></div>
    </div>

    <script>
        function updateStatus(message, type = 'info') {
            const status = document.getElementById('status');
            status.textContent = message;
            status.style.background = type === 'success' ? '#e6fffa' : 
                                   type === 'error' ? '#fed7d7' : '#e6fffa';
            status.style.borderColor = type === 'success' ? '#81e6d9' : 
                                     type === 'error' ? '#feb2b2' : '#81e6d9';
        }

        function testFocusMode() {
            updateStatus('Testing focus mode modal...');
            
            // Simulate the popup functionality
            try {
                // Create a simple focus mode modal for testing
                createTestModal();
                updateStatus('Focus mode modal created successfully!', 'success');
            } catch (error) {
                updateStatus('Error creating focus mode modal: ' + error.message, 'error');
            }
        }

        function createTestModal() {
            // Remove existing modal if any
            const existingModal = document.getElementById('focusModeModal');
            if (existingModal) {
                existingModal.remove();
            }

            // Create modal overlay
            const modalOverlay = document.createElement('div');
            modalOverlay.id = 'focusModeModal';
            modalOverlay.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: rgba(0, 0, 0, 0.7);
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 10000;
                opacity: 0;
                transition: opacity 0.3s ease;
            `;

            modalOverlay.innerHTML = `
                <div style="
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white;
                    padding: 40px;
                    border-radius: 12px;
                    text-align: center;
                    max-width: 400px;
                    width: 90%;
                    position: relative;
                ">
                    <button onclick="closeTestModal()" style="
                        position: absolute;
                        top: 10px;
                        right: 15px;
                        background: none;
                        border: none;
                        color: white;
                        font-size: 24px;
                        cursor: pointer;
                    ">×</button>
                    
                    <h2>🎯 Focus Mode</h2>
                    <div style="
                        width: 150px;
                        height: 150px;
                        border-radius: 50%;
                        background: rgba(255,255,255,0.2);
                        margin: 20px auto;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        font-size: 24px;
                        font-weight: bold;
                    " id="testTimer">25:00</div>
                    
                    <div style="margin: 20px 0;">
                        <button onclick="startTestTimer()" style="
                            background: #48bb78;
                            color: white;
                            border: none;
                            padding: 10px 20px;
                            border-radius: 6px;
                            cursor: pointer;
                            margin: 5px;
                        ">▶️ Start</button>
                        
                        <button onclick="pauseTestTimer()" style="
                            background: #ed8936;
                            color: white;
                            border: none;
                            padding: 10px 20px;
                            border-radius: 6px;
                            cursor: pointer;
                            margin: 5px;
                        ">⏸️ Pause</button>
                        
                        <button onclick="stopTestTimer()" style="
                            background: #f56565;
                            color: white;
                            border: none;
                            padding: 10px 20px;
                            border-radius: 6px;
                            cursor: pointer;
                            margin: 5px;
                        ">⏹️ Stop</button>
                    </div>
                    
                    <p style="font-size: 14px; opacity: 0.9;">
                        This is a test of the focus mode modal functionality.
                    </p>
                </div>
            `;

            document.body.appendChild(modalOverlay);

            // Show modal with animation
            setTimeout(() => {
                modalOverlay.style.opacity = '1';
            }, 10);

            // Close on overlay click
            modalOverlay.addEventListener('click', (e) => {
                if (e.target === modalOverlay) {
                    closeTestModal();
                }
            });
        }

        function closeTestModal() {
            const modal = document.getElementById('focusModeModal');
            if (modal) {
                modal.style.opacity = '0';
                setTimeout(() => {
                    modal.remove();
                    updateStatus('Focus mode modal closed.', 'success');
                }, 300);
            }
        }

        let testTimerInterval = null;
        let testTime = 25 * 60; // 25 minutes in seconds

        function startTestTimer() {
            if (testTimerInterval) clearInterval(testTimerInterval);
            
            testTimerInterval = setInterval(() => {
                testTime--;
                updateTestTimer();
                
                if (testTime <= 0) {
                    clearInterval(testTimerInterval);
                    alert('Timer finished!');
                    testTime = 25 * 60;
                    updateTestTimer();
                }
            }, 1000);
            
            updateStatus('Test timer started!', 'success');
        }

        function pauseTestTimer() {
            if (testTimerInterval) {
                clearInterval(testTimerInterval);
                testTimerInterval = null;
                updateStatus('Test timer paused.', 'info');
            }
        }

        function stopTestTimer() {
            if (testTimerInterval) {
                clearInterval(testTimerInterval);
                testTimerInterval = null;
            }
            testTime = 25 * 60;
            updateTestTimer();
            updateStatus('Test timer stopped.', 'info');
        }

        function updateTestTimer() {
            const timerElement = document.getElementById('testTimer');
            if (timerElement) {
                const minutes = Math.floor(testTime / 60);
                const seconds = testTime % 60;
                timerElement.textContent = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
            }
        }

        function testTimer() {
            updateStatus('Timer functions are working correctly!', 'success');
        }

        function testSettings() {
            updateStatus('Settings functionality would be tested here.', 'info');
        }

        // Initialize
        updateStatus('Focus Mode Test Page Ready');
    </script>
</body>
</html>
